<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- الثيم الأساسي -->
    <style name="Theme.CurrencyExchange" parent="Theme.Material3.DayNight">
        <!-- الألوان الأساسية -->
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <!-- الألوان الثانوية -->
        <item name="colorSecondary">@color/accent_color</item>
        <item name="colorSecondaryVariant">@color/accent_dark</item>
        <item name="colorOnSecondary">@color/white</item>
        
        <!-- ألوان الخلفية -->
        <item name="android:colorBackground">@color/background_color</item>
        <item name="colorSurface">@color/surface_color</item>
        <item name="colorOnBackground">@color/text_color</item>
        <item name="colorOnSurface">@color/text_color</item>
        
        <!-- ألوان الحالة -->
        <item name="colorError">@color/error_color</item>
        <item name="colorOnError">@color/white</item>
        
        <!-- شريط الحالة -->
        <item name="android:statusBarColor">@color/status_bar_color</item>
        <item name="android:windowLightStatusBar">false</item>
        
        <!-- شريط التنقل -->
        <item name="android:navigationBarColor">@color/navigation_bar_color</item>
        
        <!-- إعدادات النوافذ -->
        <item name="android:windowBackground">@color/background_color</item>
        <item name="android:windowContentTransitions">true</item>
        <item name="android:windowActivityTransitions">true</item>
    </style>

    <!-- ثيم بدون شريط الأدوات -->
    <style name="Theme.CurrencyExchange.NoActionBar" parent="Theme.CurrencyExchange">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <!-- ثيم شاشة البداية -->
    <style name="Theme.CurrencyExchange.Splash" parent="Theme.CurrencyExchange.NoActionBar">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowIsTranslucent">false</item>
    </style>

    <!-- أنماط النصوص -->
    <style name="TextAppearance.CurrencyExchange.Headline1" parent="TextAppearance.Material3.HeadlineLarge">
        <item name="android:textColor">@color/text_color</item>
        <item name="android:textSize">32sp</item>
        <item name="android:fontFamily">@font/arabic_font_bold</item>
    </style>

    <style name="TextAppearance.CurrencyExchange.Headline2" parent="TextAppearance.Material3.HeadlineMedium">
        <item name="android:textColor">@color/text_color</item>
        <item name="android:textSize">24sp</item>
        <item name="android:fontFamily">@font/arabic_font_bold</item>
    </style>

    <style name="TextAppearance.CurrencyExchange.Body1" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:textColor">@color/text_color</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">@font/arabic_font</item>
    </style>

    <style name="TextAppearance.CurrencyExchange.Body2" parent="TextAppearance.Material3.BodyMedium">
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:textSize">14sp</item>
        <item name="android:fontFamily">@font/arabic_font</item>
    </style>

    <!-- أنماط الأزرار -->
    <style name="Widget.CurrencyExchange.Button" parent="Widget.Material3.Button">
        <item name="android:textColor">@color/white</item>
        <item name="backgroundTint">@color/primary_color</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:fontFamily">@font/arabic_font_bold</item>
        <item name="android:textSize">16sp</item>
        <item name="android:minHeight">48dp</item>
    </style>

    <style name="Widget.CurrencyExchange.Button.Secondary" parent="Widget.CurrencyExchange.Button">
        <item name="backgroundTint">@color/accent_color</item>
    </style>

    <style name="Widget.CurrencyExchange.Button.Outlined" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:textColor">@color/primary_color</item>
        <item name="strokeColor">@color/primary_color</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:fontFamily">@font/arabic_font_bold</item>
    </style>

    <!-- أنماط البطاقات -->
    <style name="Widget.CurrencyExchange.CardView" parent="Widget.Material3.CardView.Elevated">
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
        <item name="cardBackgroundColor">@color/card_background</item>
        <item name="contentPadding">16dp</item>
    </style>

    <!-- أنماط حقول الإدخال -->
    <style name="Widget.CurrencyExchange.TextInputLayout" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/primary_color</item>
        <item name="hintTextColor">@color/text_hint</item>
        <item name="android:textColorHint">@color/text_hint</item>
        <item name="boxCornerRadiusTopStart">8dp</item>
        <item name="boxCornerRadiusTopEnd">8dp</item>
        <item name="boxCornerRadiusBottomStart">8dp</item>
        <item name="boxCornerRadiusBottomEnd">8dp</item>
    </style>

    <!-- أنماط شريط الأدوات -->
    <style name="Widget.CurrencyExchange.Toolbar" parent="Widget.Material3.Toolbar">
        <item name="android:background">@color/primary_color</item>
        <item name="titleTextColor">@color/white</item>
        <item name="subtitleTextColor">@color/white</item>
        <item name="android:theme">@style/ThemeOverlay.Material3.Dark.ActionBar</item>
    </style>

    <!-- أنماط التبويبات -->
    <style name="Widget.CurrencyExchange.TabLayout" parent="Widget.Material3.TabLayout">
        <item name="tabTextColor">@color/white</item>
        <item name="tabSelectedTextColor">@color/white</item>
        <item name="tabIndicatorColor">@color/accent_color</item>
        <item name="android:background">@color/primary_color</item>
    </style>

    <!-- أنماط شريط التقدم -->
    <style name="Widget.CurrencyExchange.ProgressBar" parent="Widget.Material3.CircularProgressIndicator">
        <item name="indicatorColor">@color/primary_color</item>
        <item name="trackColor">@color/primary_light</item>
    </style>

    <!-- أنماط الفواصل -->
    <style name="Widget.CurrencyExchange.Divider">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">1dp</item>
        <item name="android:background">@color/divider_color</item>
    </style>

</resources>
