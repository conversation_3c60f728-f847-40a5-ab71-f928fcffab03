# Generated by Django 5.2 on 2025-07-27 15:21

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='customuser',
            name='id_document_image',
        ),
        migrations.RemoveField(
            model_name='customuser',
            name='selfie_image',
        ),
        migrations.AddField(
            model_name='customuser',
            name='document_back_image',
            field=models.ImageField(blank=True, help_text='صورة واضحة للوجه الخلفي من الوثيقة (إذا كان متوفراً)', null=True, upload_to='documents/back/', verbose_name='صورة الوثيقة - الوجه الخلفي'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='document_front_image',
            field=models.ImageField(blank=True, help_text='صورة واضحة للوجه الأمامي من الوثيقة', null=True, upload_to='documents/front/', verbose_name='صورة الوثيقة - الوجه الأمامي'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='document_type',
            field=models.CharField(blank=True, choices=[('national_id', 'بطاقة الهوية الوطنية'), ('passport', 'جواز السفر'), ('driving_license', 'رخصة السياقة')], help_text='اختر نوع الوثيقة التي ستستخدمها للتحقق', max_length=20, null=True, verbose_name='نوع الوثيقة'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='full_name_on_document',
            field=models.CharField(blank=True, help_text='اكتب اسمك الكامل تماماً كما يظهر في الوثيقة الرسمية', max_length=255, null=True, verbose_name='الاسم الكامل كما يظهر في الوثيقة'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='selfie_with_document',
            field=models.ImageField(blank=True, help_text='صورة سيلفي واضحة وأنت تحمل الوثيقة بجانب وجهك', null=True, upload_to='selfie_with_document/', verbose_name='صورة سيلفي مع الوثيقة'),
        ),
    ]
