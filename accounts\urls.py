from django.urls import path
from django.contrib.auth import views as auth_views
from . import views

app_name = 'accounts'

urlpatterns = [
    # دليل التحقق من الهوية
    path('verification-guide/', views.verification_guide, name='verification_guide'),

    # التصوير المباشر
    path('camera-capture/', views.camera_capture, name='camera_capture'),

    # تسجيل مستخدم جديد
    path('register/', views.RegisterView.as_view(), name='register'),
    path('registration-success/', views.registration_success, name='registration_success'),

    # تسجيل الدخول والخروج
    path('login/', views.custom_login, name='login'),
    path('logout/', auth_views.LogoutView.as_view(), name='logout'),

    # الملف الشخصي
    path('profile/', views.profile_view, name='profile'),
    path('verification-status/', views.verification_status, name='verification_status'),
]
