# إعدادات المتصفح لتمكين الكاميرا

## المشكلة
المتصفحات الحديثة تحجب الوصول للكاميرا على مواقع HTTP (غير المشفرة) لأسباب أمنية.

## الحلول

### الحل الأول: استخدام localhost ✅ (الأسهل)
**الرابط الجديد:** http://localhost:8000/

المتصفحات تسمح بالوصول للكاميرا على localhost حتى مع HTTP.

### الحل الثاني: تمكين الكاميرا يدوياً في Chrome

#### الطريقة الأولى - إعدادات الموقع:
1. افتح Chrome واذهب إلى الموقع
2. انقر على أيقونة القفل 🔒 في شريط العنوان
3. انقر على "إعدادات الموقع" أو "Site settings"
4. ابحث عن "الكاميرا" أو "Camera"
5. اختر "السماح" أو "Allow"
6. أعد تحميل الصفحة

#### الطريقة الثانية - الإعدادات العامة:
1. افتح Chrome
2. اذهب إلى: `chrome://settings/content/camera`
3. تأكد من أن "السماح للمواقع باستخدام الكاميرا" مفعل
4. في قسم "السماح"، أضف الموقع: `http://***********:8000`

#### الطريقة الثالثة - تشغيل Chrome مع إعدادات خاصة:
```bash
chrome.exe --unsafely-treat-insecure-origin-as-secure=http://***********:8000 --user-data-dir=C:/temp/chrome-dev
```

### الحل الثالث: تمكين الكاميرا في Firefox

1. افتح Firefox واذهب إلى الموقع
2. انقر على أيقونة الكاميرا في شريط العنوان
3. اختر "السماح" أو "Allow"
4. أو اذهب إلى: `about:preferences#privacy`
5. ابحث عن "الأذونات" أو "Permissions"
6. انقر على "إعدادات" بجانب "الكاميرا"
7. أضف الموقع واختر "السماح"

### الحل الرابع: تمكين الكاميرا في Edge

1. افتح Edge
2. اذهب إلى: `edge://settings/content/camera`
3. تأكد من أن "السماح للمواقع بالوصول للكاميرا" مفعل
4. أضف الموقع في قائمة "السماح"

## اختبار الحل

بعد تطبيق أي من الحلول أعلاه:

1. افتح الرابط: **http://localhost:8000/**
2. اذهب إلى صفحة "التصوير المباشر"
3. اضغط على "اختبار الكاميرا"
4. يجب أن تظهر رسالة طلب الإذن
5. اضغط على "السماح" أو "Allow"

## نصائح إضافية

### للتأكد من عمل الكاميرا:
- جرب موقع: https://webcamtests.com/
- أو موقع: https://www.onlinemictest.com/webcam-test/

### إذا لم تعمل الحلول:
1. أعد تشغيل المتصفح
2. امسح الكاش والكوكيز
3. جرب متصفح آخر
4. تأكد من أن الكاميرا تعمل في تطبيقات أخرى

### للأجهزة المحمولة:
- تأكد من إعطاء إذن الكاميرا للمتصفح في إعدادات النظام
- جرب فتح الموقع في متصفح مختلف (Chrome, Firefox, Safari)

## الرابط المحدث
**استخدم هذا الرابط:** http://localhost:8000/

هذا الرابط يجب أن يعمل بدون مشاكل في جميع المتصفحات! 🎉