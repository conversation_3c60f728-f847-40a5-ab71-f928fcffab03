# تطبيق تداول العملات - الجزائر

تطبيق ويب لتداول العملات مصمم خصيصاً للسوق الجزائري، يتيح للمستخدمين شراء وبيع العملات (الدولار الأمريكي، اليورو، الدينار الجزائري) فيما بينهم.

## المميزات الرئيسية

### 🔐 نظام المستخدمين المتقدم
- تسجيل المستخدمين مع التحقق المتقدم من الهوية
- دعم أنواع متعددة من الوثائق:
  - بطاقة الهوية الوطنية (وجه أمامي وخلفي)
  - جواز السفر (الصفحة الأولى فقط)
  - رخصة السياقة (وجه أمامي وخلفي)
- صورة سيلفي مع الوثيقة للتأكد من الهوية
- تسمية المستخدم حسب الاسم الموجود في الوثيقة
- نظام موافقة المشرف مع ملاحظات مفصلة
- دليل شامل لتعليمات التصوير الصحيح
- لا يتطلب OTP أو تأكيد البريد الإلكتروني

### 💱 إدارة العروض
- إنشاء عروض بيع وشراء للعملات
- تحديد الكمية والسعر
- تتبع الكمية المتبقية
- إحصائيات مفصلة للعروض

### 💬 نظام الرسائل
- رسائل خاصة بين المستخدمين
- مرتبط بالعروض المحددة
- واجهة سهلة الاستخدام

### 🎨 واجهة المستخدم
- تصميم عربي بالكامل (RTL)
- متجاوب مع الأجهزة المحمولة (Mobile-first)
- استخدام Bootstrap RTL
- خط Cairo للنصوص العربية

## التقنيات المستخدمة

- **Backend**: Django 5.2, Python 3.13
- **Database**: SQLite (للتطوير)
- **Frontend**: Bootstrap 5.3 RTL, Font Awesome, Cairo Font
- **Image Processing**: Pillow
- **Language**: العربية (ar) مع دعم الإنجليزية

## متطلبات التشغيل

```bash
Python 3.13+
Django 5.2
Pillow
```

## التثبيت والتشغيل

### 1. استنساخ المشروع
```bash
git clone <repository-url>
cd currency_exchange
```

### 2. تثبيت المتطلبات
```bash
pip install django pillow
```

### 3. إعداد قاعدة البيانات
```bash
python manage.py makemigrations
python manage.py migrate
```

### 4. إنشاء مشرف
```bash
python manage.py createsuperuser
```

### 5. إنشاء بيانات تجريبية (اختياري)
```bash
python manage.py create_sample_data --users 5 --offers 10
```

### 6. تشغيل الخادم
```bash
python manage.py runserver
```

الآن يمكنك الوصول للتطبيق على: http://127.0.0.1:8000

### الصفحات الرئيسية

- **الصفحة الرئيسية:** `/` - عرض العروض المتاحة
- **دليل التحقق من الهوية:** `/accounts/verification-guide/` - تعليمات مفصلة للتصوير
- **تسجيل الدخول:** `/accounts/login/`
- **تسجيل حساب جديد:** `/accounts/register/`
- **الملف الشخصي:** `/accounts/profile/`
- **حالة التحقق:** `/accounts/verification-status/`
- **إنشاء عرض:** `/offers/create/`
- **الرسائل:** `/messages/`
- **لوحة المشرف:** `/admin/`

## هيكل المشروع

```
currency_exchange/
├── accounts/              # تطبيق إدارة المستخدمين
│   ├── models.py         # نموذج المستخدم المخصص
│   ├── forms.py          # نماذج التسجيل والدخول
│   ├── views.py          # عروض المستخدمين
│   └── admin.py          # إدارة المستخدمين في لوحة المشرف
├── offers/               # تطبيق إدارة العروض
│   ├── models.py         # نموذج العروض
│   ├── forms.py          # نماذج العروض
│   ├── views.py          # عروض العروض
│   └── admin.py          # إدارة العروض في لوحة المشرف
├── messaging/            # تطبيق الرسائل
│   ├── models.py         # نماذج المحادثات والرسائل
│   └── views.py          # عروض الرسائل
├── templates/            # قوالب HTML
│   ├── base.html         # القالب الأساسي
│   ├── accounts/         # قوالب المستخدمين
│   └── offers/           # قوالب العروض
├── static/               # الملفات الثابتة
│   └── css/
│       └── custom.css    # تنسيقات مخصصة
└── media/                # ملفات المستخدمين المرفوعة
```

## الاستخدام

### للمستخدمين العاديين:
1. التسجيل برفع صور الهوية والسيلفي
2. انتظار موافقة المشرف
3. إنشاء عروض بيع أو شراء
4. تصفح العروض المتاحة
5. التواصل مع أصحاب العروض

### للمشرفين:
1. الدخول إلى لوحة المشرف: `/admin/`
2. مراجعة طلبات التحقق من المستخدمين
3. قبول أو رفض الحسابات
4. مراقبة العروض والمحتوى

## الأمان

- **نظام تحقق متقدم من الهوية:**
  - دعم ثلاثة أنواع من الوثائق الرسمية
  - تصوير الوجهين للوثائق ذات الوجهين
  - صورة سيلفي مع الوثيقة لمنع الانتحال
  - التحقق من مطابقة الاسم في الوثيقة
- **مراجعة يدوية شاملة:**
  - فحص دقيق لجميع الوثائق من قبل المشرف
  - إمكانية إضافة ملاحظات مفصلة
  - نظام قبول/رفض مع تبرير الأسباب
- **حماية البيانات:**
  - تشفير جميع الصور المرفوعة
  - حماية من CSRF في جميع النماذج
  - عدم استخدام البيانات لأغراض أخرى

## التطوير المستقبلي

- [ ] تحويل إلى تطبيق Android APK
- [ ] إضافة نظام دفع إلكتروني
- [ ] إضافة إشعارات فورية
- [ ] تحسين نظام البحث والفلترة
- [ ] إضافة تقييمات المستخدمين

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى إنشاء issue في المستودع.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.
