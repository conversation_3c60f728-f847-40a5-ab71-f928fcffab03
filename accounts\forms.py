from django import forms
from django.contrib.auth.forms import UserCreationForm, AuthenticationForm
from django.utils.translation import gettext_lazy as _
from .models import CustomUser


class CustomUserRegistrationForm(UserCreationForm):
    """
    نموذج تسجيل المستخدم الجديد
    """
    email = forms.EmailField(
        label=_('البريد الإلكتروني'),
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': _('أدخل بريدك الإلكتروني'),
            'dir': 'ltr'
        })
    )
    
    username = forms.CharField(
        label=_('اسم المستخدم'),
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('أدخل اسم المستخدم')
        })
    )
    
    password1 = forms.CharField(
        label=_('كلمة السر'),
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': _('أدخل كلمة السر')
        })
    )
    
    password2 = forms.CharField(
        label=_('تأكيد كلمة السر'),
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': _('أعد إدخال كلمة السر')
        })
    )
    
    document_type = forms.ChoiceField(
        label=_('نوع الوثيقة'),
        choices=CustomUser.DOCUMENT_TYPE_CHOICES,
        widget=forms.Select(attrs={
            'class': 'form-control',
            'onchange': 'toggleBackImageField()'
        }),
        help_text=_('اختر نوع الوثيقة التي ستستخدمها للتحقق')
    )

    full_name_on_document = forms.CharField(
        label=_('الاسم الكامل كما يظهر في الوثيقة'),
        max_length=255,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('اكتب اسمك تماماً كما يظهر في الوثيقة')
        }),
        help_text=_('يجب أن يطابق الاسم المكتوب هنا الاسم الموجود في الوثيقة بالضبط')
    )

    document_front_image = forms.ImageField(
        label=_('صورة الوثيقة - الوجه الأمامي'),
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': 'image/*'
        }),
        help_text=_('صورة واضحة للوجه الأمامي من الوثيقة')
    )

    document_back_image = forms.ImageField(
        label=_('صورة الوثيقة - الوجه الخلفي'),
        required=False,
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': 'image/*'
        }),
        help_text=_('صورة واضحة للوجه الخلفي من الوثيقة (مطلوب لبطاقة الهوية ورخصة السياقة)')
    )

    selfie_with_document = forms.ImageField(
        label=_('صورة سيلفي مع الوثيقة'),
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': 'image/*'
        }),
        help_text=_('صورة سيلفي واضحة وأنت تحمل الوثيقة بجانب وجهك')
    )

    # حقل فيديو التحقق من الوجه
    face_verification_video = forms.FileField(
        label=_('فيديو التحقق من الوجه'),
        required=False,
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': 'video/*',
            'capture': 'user'
        }),
        help_text=_('فيديو قصير (5-10 ثواني) للوجه للتحقق من أن الهوية حقيقية')
    )

    class Meta:
        model = CustomUser
        fields = ['username', 'email', 'password1', 'password2', 'document_type',
                 'full_name_on_document', 'document_front_image', 'document_back_image',
                 'selfie_with_document', 'face_verification_video']
    
    def clean_email(self):
        email = self.cleaned_data.get('email')
        if CustomUser.objects.filter(email=email).exists():
            raise forms.ValidationError(_('هذا البريد الإلكتروني مستخدم بالفعل'))
        return email

    def clean(self):
        cleaned_data = super().clean()
        document_type = cleaned_data.get('document_type')
        document_back_image = cleaned_data.get('document_back_image')

        # التحقق من أن الوجه الخلفي مطلوب لبطاقة الهوية ورخصة السياقة
        if document_type in ['national_id', 'driving_license'] and not document_back_image:
            raise forms.ValidationError({
                'document_back_image': _('صورة الوجه الخلفي مطلوبة لبطاقة الهوية ورخصة السياقة')
            })

        return cleaned_data

    def save(self, commit=True):
        user = super().save(commit=False)
        user.email = self.cleaned_data['email']
        user.verification_status = 'pending'
        if commit:
            user.save()
        return user


class CustomAuthenticationForm(AuthenticationForm):
    """
    نموذج تسجيل الدخول المخصص
    """
    username = forms.EmailField(
        label=_('البريد الإلكتروني'),
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': _('أدخل بريدك الإلكتروني'),
            'dir': 'ltr'
        })
    )
    
    password = forms.CharField(
        label=_('كلمة السر'),
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': _('أدخل كلمة السر')
        })
    )
    
    def clean(self):
        username = self.cleaned_data.get('username')
        password = self.cleaned_data.get('password')
        
        if username and password:
            # البحث عن المستخدم بالبريد الإلكتروني
            try:
                user = CustomUser.objects.get(email=username)
                self.cleaned_data['username'] = user.username
            except CustomUser.DoesNotExist:
                raise forms.ValidationError(_('البريد الإلكتروني أو كلمة السر غير صحيحة'))
        
        return super().clean()


class UserProfileForm(forms.ModelForm):
    """
    نموذج تحديث ملف المستخدم الشخصي
    """
    class Meta:
        model = CustomUser
        fields = ['username', 'email', 'first_name', 'last_name']
        widgets = {
            'username': forms.TextInput(attrs={'class': 'form-control'}),
            'email': forms.EmailInput(attrs={'class': 'form-control', 'dir': 'ltr'}),
            'first_name': forms.TextInput(attrs={'class': 'form-control'}),
            'last_name': forms.TextInput(attrs={'class': 'form-control'}),
        }
        labels = {
            'username': _('اسم المستخدم'),
            'email': _('البريد الإلكتروني'),
            'first_name': _('الاسم الأول'),
            'last_name': _('اسم العائلة'),
        }
