{% extends 'base.html' %}

{% block title %}العروض - تداول العملات{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="fas fa-exchange-alt me-2"></i>عروض تداول العملات</h2>
            <p class="text-muted">تصفح العروض المتاحة للبيع والشراء</p>
        </div>
        <div class="col-md-4 text-end">
            {% if user.is_authenticated and user.is_verified %}
                <a href="{% url 'offers:create_offer' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>
                    إنشاء عرض جديد
                </a>
            {% endif %}
        </div>
    </div>
    
    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">نوع العملة</label>
                    <select name="currency" class="form-control">
                        <option value="">جميع العملات</option>
                        {% for value, label in currency_choices %}
                            <option value="{{ value }}" {% if current_currency == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">نوع العملية</label>
                    <select name="offer_type" class="form-control">
                        <option value="">جميع العمليات</option>
                        {% for value, label in offer_type_choices %}
                            <option value="{{ value }}" {% if current_offer_type == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="col-md-4">
                    <label class="form-label">البحث</label>
                    <input type="text" name="search" class="form-control" 
                           placeholder="البحث في الملاحظات..." 
                           value="{{ current_search|default:'' }}">
                </div>
                
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Offers List -->
    <div class="row">
        {% for offer in page_obj %}
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card offer-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div>
                            <span class="badge currency-badge 
                                {% if offer.currency == 'USD' %}bg-success
                                {% elif offer.currency == 'EUR' %}bg-primary
                                {% else %}bg-secondary{% endif %}">
                                {{ offer.get_currency_display }}
                            </span>
                        </div>
                        <div>
                            <span class="badge 
                                {% if offer.offer_type == 'buy' %}bg-info
                                {% else %}bg-warning{% endif %}">
                                {{ offer.get_offer_type_display }}
                            </span>
                        </div>
                    </div>
                    
                    <h5 class="card-title">
                        {{ offer.amount }} {{ offer.get_currency_display }}
                    </h5>
                    
                    <p class="card-text">
                        <strong>السعر:</strong> {{ offer.price }} دج<br>
                        <strong>المتبقي:</strong> {{ offer.remaining_amount }}<br>
                        <strong>صاحب العرض:</strong> {{ offer.user.username }}
                    </p>
                    
                    {% if offer.notes %}
                    <p class="card-text text-muted small">
                        {{ offer.notes|truncatewords:10 }}
                    </p>
                    {% endif %}
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            {{ offer.created_at|date:"d/m/Y" }}
                        </small>
                        <a href="{% url 'offers:offer_detail' offer.pk %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye me-1"></i>
                            التفاصيل
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد عروض متاحة</h4>
                <p class="text-muted">جرب تغيير معايير البحث أو</p>
                {% if user.is_authenticated and user.is_verified %}
                    <a href="{% url 'offers:create_offer' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        إنشاء أول عرض لك
                    </a>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>
    
    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <nav aria-label="تصفح الصفحات">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if current_currency %}&currency={{ current_currency }}{% endif %}{% if current_offer_type %}&offer_type={{ current_offer_type }}{% endif %}{% if current_search %}&search={{ current_search }}{% endif %}">
                        السابق
                    </a>
                </li>
            {% endif %}
            
            {% for num in page_obj.paginator.page_range %}
                {% if page_obj.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}{% if current_currency %}&currency={{ current_currency }}{% endif %}{% if current_offer_type %}&offer_type={{ current_offer_type }}{% endif %}{% if current_search %}&search={{ current_search }}{% endif %}">
                            {{ num }}
                        </a>
                    </li>
                {% endif %}
            {% endfor %}
            
            {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if current_currency %}&currency={{ current_currency }}{% endif %}{% if current_offer_type %}&offer_type={{ current_offer_type }}{% endif %}{% if current_search %}&search={{ current_search }}{% endif %}">
                        التالي
                    </a>
                </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div>
{% endblock %}
