@echo off
echo ========================================
echo    تطبيق تداول العملات - بناء APK
echo ========================================
echo.

REM التحقق من وجود Java
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Java غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Java JDK 8 أو أحدث
    pause
    exit /b 1
)

REM التحقق من وجود Android SDK
if not defined ANDROID_HOME (
    echo تحذير: متغير ANDROID_HOME غير محدد
    echo يرجى تعيين مسار Android SDK
    echo مثال: set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
    echo.
)

echo جاري تنظيف المشروع...
call gradlew.bat clean
if %errorlevel% neq 0 (
    echo خطأ في تنظيف المشروع
    pause
    exit /b 1
)

echo.
echo جاري بناء APK...
call gradlew.bat assembleDebug
if %errorlevel% neq 0 (
    echo خطأ في بناء APK
    pause
    exit /b 1
)

echo.
echo ========================================
echo تم بناء APK بنجاح!
echo ========================================
echo.
echo موقع الملف:
echo app\build\outputs\apk\debug\app-debug.apk
echo.

REM فتح مجلد APK
if exist "app\build\outputs\apk\debug\" (
    echo فتح مجلد APK...
    start "" "app\build\outputs\apk\debug\"
)

echo.
echo لتثبيت APK على الهاتف:
echo 1. انسخ ملف app-debug.apk إلى الهاتف
echo 2. فعل "مصادر غير معروفة" في إعدادات الأمان
echo 3. اضغط على ملف APK لتثبيته
echo.
pause
