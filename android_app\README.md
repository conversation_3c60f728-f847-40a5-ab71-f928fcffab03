# تطبيق تداول العملات - Android APK

تطبيق Android يعرض موقع Django لتداول العملات باستخدام WebView.

## المتطلبات

### 1. Android Studio
- تحميل وتثبيت [Android Studio](https://developer.android.com/studio)
- إصدار Android Studio: Arctic Fox أو أحدث
- Android SDK: API Level 21 (Android 5.0) أو أحدث

### 2. Java Development Kit (JDK)
- JDK 8 أو أحدث
- يمكن تحميله من [Oracle](https://www.oracle.com/java/technologies/downloads/) أو [OpenJDK](https://openjdk.org/)

## خطوات البناء

### الطريقة الأولى: باستخدام Android Studio

1. **فتح المشروع:**
   ```
   - افتح Android Studio
   - اختر "Open an existing Android Studio project"
   - اختر مجلد android_app
   ```

2. **تحديث إعدادات الخادم:**
   ```java
   // في ملف MainActivity.java، غير رابط الخادم إذا لزم الأمر
   private static final String SERVER_URL = "https://192.168.1.4:8000/";
   ```

3. **بناء التطبيق:**
   ```
   - اذهب إلى Build > Build Bundle(s) / APK(s) > Build APK(s)
   - انتظر حتى انتهاء البناء
   - ستجد ملف APK في: app/build/outputs/apk/debug/
   ```

### الطريقة الثانية: باستخدام سطر الأوامر

1. **التأكد من تثبيت Android SDK:**
   ```bash
   # تأكد من وجود متغير البيئة ANDROID_HOME
   echo $ANDROID_HOME  # في Linux/Mac
   echo %ANDROID_HOME% # في Windows
   ```

2. **بناء التطبيق:**
   ```bash
   cd android_app
   ./gradlew assembleDebug  # في Linux/Mac
   gradlew.bat assembleDebug # في Windows
   ```

3. **العثور على ملف APK:**
   ```
   app/build/outputs/apk/debug/app-debug.apk
   ```

## إعدادات مهمة

### 1. تغيير رابط الخادم
في ملف `MainActivity.java`:
```java
private static final String SERVER_URL = "https://YOUR_SERVER_IP:8000/";
```

### 2. تغيير اسم التطبيق
في ملف `strings.xml`:
```xml
<string name="app_name">اسم التطبيق الجديد</string>
```

### 3. تغيير أيقونة التطبيق
- استبدل الملفات في `app/src/main/res/mipmap-*/`
- استخدم [Android Asset Studio](https://romannurik.github.io/AndroidAssetStudio/) لإنشاء الأيقونات

## الميزات

### ✅ المتوفرة
- عرض موقع Django في WebView
- دعم الكاميرا والميكروفون
- دعم اللغة العربية
- دعم HTTPS مع شهادات محلية
- حفظ حالة التصفح
- أزرار التنقل (رجوع، تقديم، تحديث)
- إدارة الأذونات تلقائياً

### 🔄 قيد التطوير
- إشعارات push
- تحديث تلقائي
- وضع عدم الاتصال

## استكشاف الأخطاء

### مشكلة: "SDK not found"
```bash
# تأكد من تثبيت Android SDK وتعيين المتغير
export ANDROID_HOME=/path/to/android/sdk
export PATH=$PATH:$ANDROID_HOME/tools:$ANDROID_HOME/platform-tools
```

### مشكلة: "Build failed"
```bash
# نظف المشروع وأعد البناء
./gradlew clean
./gradlew assembleDebug
```

### مشكلة: "Certificate error"
- تأكد من أن الخادم Django يعمل
- تأكد من صحة عنوان IP في SERVER_URL
- تحقق من إعدادات network_security_config.xml

## التوقيع والنشر

### إنشاء مفتاح التوقيع:
```bash
keytool -genkey -v -keystore currency-exchange-key.keystore -alias currency-exchange -keyalg RSA -keysize 2048 -validity 10000
```

### بناء APK موقع:
```bash
./gradlew assembleRelease
```

## الدعم

للمساعدة أو الإبلاغ عن مشاكل:
- تأكد من تشغيل خادم Django على الرابط المحدد
- تحقق من اتصال الشبكة
- راجع ملفات السجل في Android Studio

## الإصدار
- الإصدار الحالي: 1.0.0
- تاريخ الإنشاء: 2025-01-28
- متوافق مع: Android 5.0+ (API 21+)
