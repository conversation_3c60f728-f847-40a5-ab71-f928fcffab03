# Generated by Django 5.2 on 2025-07-27 14:34

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Offer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('currency', models.CharField(choices=[('USD', 'دولار أمريكي'), ('EUR', 'يورو'), ('DZD', 'دينار جزائري')], max_length=3, verbose_name='نوع العملة')),
                ('offer_type', models.CharField(choices=[('buy', 'شراء'), ('sell', 'بيع')], max_length=4, verbose_name='نوع العملية')),
                ('price', models.DecimalField(decimal_places=2, help_text='السعر بالدينار الجزائري', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))], verbose_name='السعر')),
                ('amount', models.DecimalField(decimal_places=2, help_text='الكمية المطلوبة', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))], verbose_name='الكمية')),
                ('remaining_amount', models.DecimalField(decimal_places=2, help_text='الكمية المتبقية من العرض', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='الكمية المتبقية')),
                ('notes', models.TextField(blank=True, help_text='ملاحظات إضافية حول العرض', null=True, verbose_name='ملاحظات')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('inactive', 'غير نشط'), ('completed', 'مكتمل'), ('cancelled', 'ملغي')], default='active', max_length=10, verbose_name='حالة العرض')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ النشر')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ آخر تحديث')),
                ('expires_at', models.DateTimeField(blank=True, help_text='تاريخ انتهاء صلاحية العرض (اختياري)', null=True, verbose_name='تاريخ انتهاء العرض')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='offers', to=settings.AUTH_USER_MODEL, verbose_name='صاحب العرض')),
            ],
            options={
                'verbose_name': 'عرض',
                'verbose_name_plural': 'العروض',
                'ordering': ['-created_at'],
            },
        ),
    ]
