{% extends 'base.html' %}

{% block title %}تعديل العرض - تداول العملات{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body p-4">
                    <h3 class="card-title text-center mb-4">
                        <i class="fas fa-edit me-2"></i>
                        تعديل العرض
                    </h3>
                    
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.currency.id_for_label }}" class="form-label">
                                    <i class="fas fa-coins me-1"></i>
                                    {{ form.currency.label }}
                                </label>
                                {{ form.currency }}
                                {% if form.currency.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.currency.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.offer_type.id_for_label }}" class="form-label">
                                    <i class="fas fa-exchange-alt me-1"></i>
                                    {{ form.offer_type.label }}
                                </label>
                                {{ form.offer_type }}
                                {% if form.offer_type.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.offer_type.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.amount.id_for_label }}" class="form-label">
                                    <i class="fas fa-hashtag me-1"></i>
                                    {{ form.amount.label }}
                                </label>
                                {{ form.amount }}
                                <div class="form-text">{{ form.amount.help_text }}</div>
                                {% if form.amount.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.amount.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.price.id_for_label }}" class="form-label">
                                    <i class="fas fa-money-bill me-1"></i>
                                    {{ form.price.label }}
                                </label>
                                {{ form.price }}
                                <div class="form-text">{{ form.price.help_text }}</div>
                                {% if form.price.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.price.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.expires_at.id_for_label }}" class="form-label">
                                <i class="fas fa-calendar me-1"></i>
                                {{ form.expires_at.label }}
                            </label>
                            {{ form.expires_at }}
                            <div class="form-text">{{ form.expires_at.help_text }}</div>
                            {% if form.expires_at.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.expires_at.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-4">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">
                                <i class="fas fa-sticky-note me-1"></i>
                                {{ form.notes.label }}
                            </label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.notes.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-1"></i>
                            <strong>ملاحظة:</strong> تعديل العرض قد يؤثر على المحادثات الجارية مع المهتمين.
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-warning flex-fill">
                                <i class="fas fa-save me-1"></i>
                                حفظ التغييرات
                            </button>
                            <a href="{% url 'offers:offer_detail' offer.pk %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>
                                إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- معلومات العرض الحالي -->
            <div class="card mt-3">
                <div class="card-body">
                    <h5 class="card-title">معلومات العرض الحالي</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>تاريخ النشر:</strong> {{ offer.created_at|date:"d/m/Y H:i" }}</p>
                            <p><strong>آخر تحديث:</strong> {{ offer.updated_at|date:"d/m/Y H:i" }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>الكمية المتبقية:</strong> {{ offer.remaining_amount }}</p>
                            <p><strong>حالة العرض:</strong> 
                                <span class="badge 
                                    {% if offer.status == 'active' %}bg-success
                                    {% elif offer.status == 'inactive' %}bg-secondary
                                    {% elif offer.status == 'completed' %}bg-primary
                                    {% else %}bg-danger{% endif %}">
                                    {{ offer.get_status_display }}
                                </span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// حاسبة سريعة للمبلغ الإجمالي
document.addEventListener('DOMContentLoaded', function() {
    const amountField = document.getElementById('{{ form.amount.id_for_label }}');
    const priceField = document.getElementById('{{ form.price.id_for_label }}');
    
    function calculateTotal() {
        const amount = parseFloat(amountField.value) || 0;
        const price = parseFloat(priceField.value) || 0;
        const total = amount * price;
        
        if (total > 0) {
            // إضافة عرض المبلغ الإجمالي
            let totalDisplay = document.getElementById('total-display');
            if (!totalDisplay) {
                totalDisplay = document.createElement('div');
                totalDisplay.id = 'total-display';
                totalDisplay.className = 'alert alert-light mt-2';
                priceField.parentNode.appendChild(totalDisplay);
            }
            totalDisplay.innerHTML = `<strong>المبلغ الإجمالي: ${total.toFixed(2)} دج</strong>`;
        }
    }
    
    amountField.addEventListener('input', calculateTotal);
    priceField.addEventListener('input', calculateTotal);
    
    // حساب المبلغ عند تحميل الصفحة
    calculateTotal();
});
</script>
{% endblock %}
