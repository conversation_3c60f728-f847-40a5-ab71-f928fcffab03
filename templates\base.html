<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}تداول العملات - الجزائر{% endblock %}</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome for icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    {% load static %}
    <link rel="stylesheet" href="{% static 'css/custom.css' %}">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .navbar-brand {
            font-weight: 700;
            color: #2c5aa0 !important;
        }
        
        .card {
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 15px;
        }
        
        .btn-primary {
            background-color: #2c5aa0;
            border-color: #2c5aa0;
            border-radius: 25px;
        }
        
        .btn-primary:hover {
            background-color: #1e3d6f;
            border-color: #1e3d6f;
        }
        
        .currency-badge {
            font-size: 0.9rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
        }
        
        .offer-card {
            transition: transform 0.2s;
        }
        
        .offer-card:hover {
            transform: translateY(-5px);
        }
        
        .footer {
            background-color: #2c5aa0;
            color: white;
            margin-top: auto;
        }
        
        .verification-pending {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        
        .verification-approved {
            background-color: #d1edff;
            border: 1px solid #74c0fc;
            color: #0c5460;
        }
        
        .verification-rejected {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 0 10px;
            }
            
            .card {
                margin-bottom: 1rem;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body class="d-flex flex-column min-vh-100">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="{% url 'offers:offer_list' %}">
                <i class="fas fa-exchange-alt me-2"></i>
                تداول العملات
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'offers:offer_list' %}">
                            <i class="fas fa-list me-1"></i>
                            العروض
                        </a>
                    </li>
                    {% if user.is_authenticated and user.is_verified %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'offers:create_offer' %}">
                            <i class="fas fa-plus me-1"></i>
                            إنشاء عرض
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'offers:my_offers' %}">
                            <i class="fas fa-user-circle me-1"></i>
                            عروضي
                        </a>
                    </li>
                    {% endif %}
                </ul>
                
                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            {{ user.username }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'accounts:profile' %}">
                                <i class="fas fa-user-edit me-1"></i>
                                الملف الشخصي
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'accounts:verification_status' %}">
                                <i class="fas fa-check-circle me-1"></i>
                                حالة التحقق
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'accounts:logout' %}">
                                <i class="fas fa-sign-out-alt me-1"></i>
                                تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'accounts:login' %}">
                            <i class="fas fa-sign-in-alt me-1"></i>
                            تسجيل الدخول
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'accounts:camera_capture' %}">
                            <i class="fas fa-camera me-1"></i>
                            تسجيل جديد
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Messages -->
    {% if messages %}
    <div class="container mt-3">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        {% endfor %}
    </div>
    {% endif %}
    
    <!-- Main Content -->
    <main class="flex-grow-1">
        {% block content %}{% endblock %}
    </main>
    
    <!-- Footer -->
    <footer class="footer py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">© 2025 تطبيق تداول العملات - الجزائر. جميع الحقوق محفوظة.</p>
            <small class="text-light">تطبيق مخصص للسوق الجزائري فقط</small>
        </div>
    </footer>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
