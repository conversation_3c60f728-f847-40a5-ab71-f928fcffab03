from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from offers.models import Offer
from decimal import Decimal
import random

User = get_user_model()


class Command(BaseCommand):
    help = 'إنشاء بيانات تجريبية للتطبيق'

    def add_arguments(self, parser):
        parser.add_argument(
            '--users',
            type=int,
            default=5,
            help='عدد المستخدمين المراد إنشاؤهم'
        )
        parser.add_argument(
            '--offers',
            type=int,
            default=10,
            help='عدد العروض المراد إنشاؤها'
        )

    def handle(self, *args, **options):
        users_count = options['users']
        offers_count = options['offers']

        self.stdout.write('بدء إنشاء البيانات التجريبية...')

        # إنشاء مستخدمين تجريبيين
        users = []
        for i in range(users_count):
            username = f'user{i+1}'
            email = f'user{i+1}@example.com'
            
            if not User.objects.filter(email=email).exists():
                user = User.objects.create_user(
                    username=username,
                    email=email,
                    password='password123',
                    verification_status='approved'  # موافق عليه للاختبار
                )
                users.append(user)
                self.stdout.write(f'تم إنشاء المستخدم: {username}')

        # إنشاء عروض تجريبية
        currencies = ['USD', 'EUR', 'DZD']
        offer_types = ['buy', 'sell']
        
        for i in range(offers_count):
            if users:
                user = random.choice(users)
                currency = random.choice(currencies)
                offer_type = random.choice(offer_types)
                amount = Decimal(str(random.uniform(100, 10000))).quantize(Decimal('0.01'))
                
                # أسعار تقريبية
                if currency == 'USD':
                    price = Decimal(str(random.uniform(130, 140))).quantize(Decimal('0.01'))
                elif currency == 'EUR':
                    price = Decimal(str(random.uniform(140, 150))).quantize(Decimal('0.01'))
                else:  # DZD
                    price = Decimal(str(random.uniform(0.5, 2))).quantize(Decimal('0.01'))
                
                offer = Offer.objects.create(
                    user=user,
                    currency=currency,
                    offer_type=offer_type,
                    amount=amount,
                    price=price,
                    notes=f'عرض تجريبي رقم {i+1} - {offer_type} {currency}',
                    status='active'
                )
                self.stdout.write(f'تم إنشاء العرض: {offer}')

        self.stdout.write(
            self.style.SUCCESS(
                f'تم إنشاء {users_count} مستخدم و {offers_count} عرض بنجاح!'
            )
        )
