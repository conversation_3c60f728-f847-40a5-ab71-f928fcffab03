from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from .models import CustomUser


@admin.register(CustomUser)
class CustomUserAdmin(UserAdmin):
    """
    إدارة المستخدمين في لوحة المشرف
    """
    list_display = [
        'email', 'username', 'full_name_on_document', 'document_type',
        'verification_status', 'date_joined', 'is_active',
        'view_document_front', 'view_document_back', 'view_selfie_with_document', 'view_face_video'
    ]
    list_filter = ['verification_status', 'document_type', 'is_active', 'date_joined']
    search_fields = ['email', 'username', 'full_name_on_document']
    ordering = ['-date_joined']

    # الحقول المعروضة في صفحة التفاصيل
    fieldsets = UserAdmin.fieldsets + (
        (_('معلومات التحقق من الهوية'), {
            'fields': (
                'document_type', 'full_name_on_document',
                'document_front_image', 'document_back_image',
                'selfie_with_document', 'face_verification_video',
                'verification_status', 'admin_notes'
            )
        }),
    )

    # الحقول المعروضة في صفحة إضافة مستخدم جديد
    add_fieldsets = UserAdmin.add_fieldsets + (
        (_('معلومات إضافية'), {
            'fields': (
                'email', 'document_type', 'full_name_on_document',
                'document_front_image', 'document_back_image',
                'selfie_with_document', 'face_verification_video'
            )
        }),
    )

    def view_document_front(self, obj):
        """عرض صورة الوثيقة الأمامية"""
        if obj.document_front_image:
            return format_html(
                '<a href="{}" target="_blank"><img src="{}" width="50" height="50" style="border-radius: 5px;" /></a>',
                obj.document_front_image.url,
                obj.document_front_image.url
            )
        return _('لا توجد صورة')
    view_document_front.short_description = _('الوثيقة - أمامي')

    def view_document_back(self, obj):
        """عرض صورة الوثيقة الخلفية"""
        if obj.document_back_image:
            return format_html(
                '<a href="{}" target="_blank"><img src="{}" width="50" height="50" style="border-radius: 5px;" /></a>',
                obj.document_back_image.url,
                obj.document_back_image.url
            )
        return _('غير متوفر')
    view_document_back.short_description = _('الوثيقة - خلفي')

    def view_selfie_with_document(self, obj):
        """عرض صورة السيلفي مع الوثيقة"""
        if obj.selfie_with_document:
            return format_html(
                '<a href="{}" target="_blank"><img src="{}" width="50" height="50" style="border-radius: 5px;" /></a>',
                obj.selfie_with_document.url,
                obj.selfie_with_document.url
            )
        return _('لا توجد صورة')
    view_selfie_with_document.short_description = _('سيلفي مع الوثيقة')

    def view_face_video(self, obj):
        """عرض فيديو التحقق من الوجه"""
        if obj.face_verification_video:
            return format_html(
                '<a href="{}" target="_blank"><i class="fas fa-video fa-2x text-primary"></i></a>',
                obj.face_verification_video.url
            )
        return _('لا يوجد فيديو')
    view_face_video.short_description = _('فيديو الوجه')

    actions = ['approve_users', 'reject_users']

    def approve_users(self, request, queryset):
        """قبول المستخدمين المحددين"""
        updated = queryset.update(verification_status='approved')
        self.message_user(request, f'تم قبول {updated} مستخدم.')
    approve_users.short_description = _('قبول المستخدمين المحددين')

    def reject_users(self, request, queryset):
        """رفض المستخدمين المحددين"""
        updated = queryset.update(verification_status='rejected')
        self.message_user(request, f'تم رفض {updated} مستخدم.')
    reject_users.short_description = _('رفض المستخدمين المحددين')
