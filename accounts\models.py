from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal
import uuid


class CustomUser(AbstractUser):
    """
    نموذج المستخدم المخصص لتطبيق تداول العملات
    """
    VERIFICATION_STATUS_CHOICES = [
        ('pending', _('قيد الانتظار')),
        ('approved', _('مقبول')),
        ('rejected', _('مرفوض')),
    ]

    DOCUMENT_TYPE_CHOICES = [
        ('national_id', _('بطاقة الهوية الوطنية')),
        ('passport', _('جواز السفر')),
        ('driving_license', _('رخصة السياقة')),
    ]

    email = models.EmailField(_('البريد الإلكتروني'), unique=True)

    # نوع الوثيقة المستخدمة للتحقق
    document_type = models.CharField(
        _('نوع الوثيقة'),
        max_length=20,
        choices=DOCUMENT_TYPE_CHOICES,
        blank=True,
        null=True,
        help_text=_('اختر نوع الوثيقة التي ستستخدمها للتحقق')
    )

    # الاسم كما يظهر في الوثيقة الرسمية
    full_name_on_document = models.CharField(
        _('الاسم الكامل كما يظهر في الوثيقة'),
        max_length=255,
        blank=True,
        null=True,
        help_text=_('اكتب اسمك الكامل تماماً كما يظهر في الوثيقة الرسمية')
    )

    # صور التحقق من الهوية - الوجه الأمامي
    document_front_image = models.ImageField(
        _('صورة الوثيقة - الوجه الأمامي'),
        upload_to='documents/front/',
        blank=True,
        null=True,
        help_text=_('صورة واضحة للوجه الأمامي من الوثيقة')
    )

    # صور التحقق من الهوية - الوجه الخلفي
    document_back_image = models.ImageField(
        _('صورة الوثيقة - الوجه الخلفي'),
        upload_to='documents/back/',
        blank=True,
        null=True,
        help_text=_('صورة واضحة للوجه الخلفي من الوثيقة (إذا كان متوفراً)')
    )

    # صورة السيلفي مع الوثيقة
    selfie_with_document = models.ImageField(
        _('صورة سيلفي مع الوثيقة'),
        upload_to='selfie_with_document/',
        blank=True,
        null=True,
        help_text=_('صورة سيلفي واضحة وأنت تحمل الوثيقة بجانب وجهك')
    )

    # فيديو التحقق من الوجه
    face_verification_video = models.FileField(
        _('فيديو التحقق من الوجه'),
        upload_to='face_videos/',
        blank=True,
        null=True,
        help_text=_('فيديو قصير (5-10 ثواني) للوجه للتحقق من أن الهوية حقيقية')
    )

    # حالة التحقق
    verification_status = models.CharField(
        _('حالة التحقق'),
        max_length=20,
        choices=VERIFICATION_STATUS_CHOICES,
        default='pending'
    )

    # تاريخ التسجيل
    date_joined = models.DateTimeField(_('تاريخ التسجيل'), auto_now_add=True)

    # تاريخ آخر تحديث للحالة
    status_updated_at = models.DateTimeField(_('تاريخ تحديث الحالة'), auto_now=True)

    # ملاحظات المشرف
    admin_notes = models.TextField(
        _('ملاحظات المشرف'),
        blank=True,
        null=True,
        help_text=_('ملاحظات خاصة بالمشرف حول حالة التحقق')
    )

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username']

    class Meta:
        verbose_name = _('مستخدم')
        verbose_name_plural = _('المستخدمون')

    def __str__(self):
        return f"{self.email} - {self.get_verification_status_display()}"

    @property
    def is_verified(self):
        """التحقق من أن المستخدم مقبول"""
        return self.verification_status == 'approved'

    @property
    def can_create_offers(self):
        """التحقق من أن المستخدم يمكنه إنشاء عروض"""
        return self.is_verified and self.is_active

    @property
    def kyc_completion_percentage(self):
        """حساب نسبة اكتمال KYC"""
        total_steps = 4  # Identity, Address, Financial, Risk Assessment
        completed_steps = 0

        # التحقق من الهوية
        if self.verification_status == 'approved':
            completed_steps += 1

        # التحقق من العنوان
        if hasattr(self, 'address_verification') and self.address_verification.status == 'approved':
            completed_steps += 1

        # المعلومات المالية
        if hasattr(self, 'financial_information') and self.financial_information.is_complete:
            completed_steps += 1

        # تقييم المخاطر
        if hasattr(self, 'risk_assessment') and self.risk_assessment.status == 'completed':
            completed_steps += 1

        return (completed_steps / total_steps) * 100

    @property
    def kyc_level(self):
        """تحديد مستوى KYC للمستخدم"""
        percentage = self.kyc_completion_percentage
        if percentage == 100:
            return 'full'
        elif percentage >= 75:
            return 'enhanced'
        elif percentage >= 50:
            return 'standard'
        elif percentage >= 25:
            return 'basic'
        else:
            return 'none'
