from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from offers.models import Offer

User = get_user_model()


class Conversation(models.Model):
    """
    نموذج المحادثة بين مستخدمين حول عرض معين
    """
    # المشاركون في المحادثة
    participant1 = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='conversations_as_participant1',
        verbose_name=_('المشارك الأول')
    )

    participant2 = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='conversations_as_participant2',
        verbose_name=_('المشارك الثاني')
    )

    # العرض المرتبط بالمحادثة
    offer = models.ForeignKey(
        Offer,
        on_delete=models.CASCADE,
        related_name='conversations',
        verbose_name=_('العرض')
    )

    # تاريخ بداية المحادثة
    created_at = models.DateTimeField(_('تاريخ البداية'), auto_now_add=True)

    # تاريخ آخر رسالة
    last_message_at = models.DateTimeField(_('تاريخ آخر رسالة'), auto_now=True)

    # حالة المحادثة
    is_active = models.BooleanField(_('نشطة'), default=True)

    class Meta:
        verbose_name = _('محادثة')
        verbose_name_plural = _('المحادثات')
        unique_together = ['participant1', 'participant2', 'offer']
        ordering = ['-last_message_at']

    def __str__(self):
        return f"محادثة بين {self.participant1.email} و {self.participant2.email} حول {self.offer}"

    def get_other_participant(self, user):
        """الحصول على المشارك الآخر في المحادثة"""
        if user == self.participant1:
            return self.participant2
        return self.participant1


class Message(models.Model):
    """
    نموذج الرسالة داخل المحادثة
    """
    # المحادثة التي تنتمي إليها الرسالة
    conversation = models.ForeignKey(
        Conversation,
        on_delete=models.CASCADE,
        related_name='messages',
        verbose_name=_('المحادثة')
    )

    # مرسل الرسالة
    sender = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='sent_messages',
        verbose_name=_('المرسل')
    )

    # محتوى الرسالة
    content = models.TextField(_('محتوى الرسالة'))

    # تاريخ الإرسال
    sent_at = models.DateTimeField(_('تاريخ الإرسال'), auto_now_add=True)

    # هل تم قراءة الرسالة
    is_read = models.BooleanField(_('مقروءة'), default=False)

    # تاريخ القراءة
    read_at = models.DateTimeField(_('تاريخ القراءة'), blank=True, null=True)

    class Meta:
        verbose_name = _('رسالة')
        verbose_name_plural = _('الرسائل')
        ordering = ['sent_at']

    def __str__(self):
        return f"رسالة من {self.sender.email} في {self.sent_at.strftime('%Y-%m-%d %H:%M')}"

    def mark_as_read(self):
        """تحديد الرسالة كمقروءة"""
        if not self.is_read:
            self.is_read = True
            from django.utils import timezone
            self.read_at = timezone.now()
            self.save()
