# نظام التحقق المتقدم من الهوية

## نظرة عامة

تم تطوير نظام تحقق متقدم من الهوية لضمان أمان وموثوقية منصة تداول العملات. يدعم النظام ثلاثة أنواع من الوثائق الرسمية مع متطلبات تصوير مختلفة لكل نوع.

## أنواع الوثائق المدعومة

### 1. بطاقة الهوية الوطنية (`national_id`)
- **الصور المطلوبة:** وجه أمامي + وجه خلفي + سيلفي مع الوثيقة
- **السبب:** بطاقة الهوية تحتوي على معلومات مهمة في الوجهين

### 2. جواز السفر (`passport`)
- **الصور المطلوبة:** الصفحة الأولى فقط + سيلفي مع الوثيقة
- **السبب:** جواز السفر يحتوي على جميع المعلومات في الصفحة الأولى

### 3. رخصة السياقة (`driving_license`)
- **الصور المطلوبة:** وجه أمامي + وجه خلفي + سيلفي مع الوثيقة
- **السبب:** رخصة السياقة تحتوي على معلومات مهمة في الوجهين

## هيكل قاعدة البيانات

### حقول نموذج CustomUser الجديدة

```python
class CustomUser(AbstractUser):
    # حقول التحقق المحسنة
    document_type = models.CharField(
        max_length=20,
        choices=DOCUMENT_TYPE_CHOICES,
        verbose_name='نوع الوثيقة'
    )
    full_name_on_document = models.CharField(
        max_length=255,
        verbose_name='الاسم الكامل كما يظهر في الوثيقة'
    )
    document_front_image = models.ImageField(
        upload_to='documents/front/',
        verbose_name='صورة الوثيقة - الوجه الأمامي'
    )
    document_back_image = models.ImageField(
        upload_to='documents/back/',
        blank=True, null=True,
        verbose_name='صورة الوثيقة - الوجه الخلفي'
    )
    selfie_with_document = models.ImageField(
        upload_to='selfies/',
        verbose_name='صورة سيلفي مع الوثيقة'
    )
```

## منطق التحقق

### في النموذج (forms.py)

```python
def clean(self):
    cleaned_data = super().clean()
    document_type = cleaned_data.get('document_type')
    document_back_image = cleaned_data.get('document_back_image')
    
    # التحقق من وجود الصورة الخلفية للوثائق التي تتطلبها
    if document_type in ['national_id', 'driving_license']:
        if not document_back_image:
            raise ValidationError({
                'document_back_image': 'صورة الوجه الخلفي مطلوبة لهذا النوع من الوثائق.'
            })
    
    return cleaned_data
```

### في JavaScript (register.html)

```javascript
function toggleBackImageField() {
    const documentType = document.getElementById('id_document_type').value;
    const backImageField = document.getElementById('back-image-field');
    const backImageInput = document.getElementById('id_document_back_image');
    
    if (documentType === 'passport') {
        backImageField.style.display = 'none';
        backImageInput.required = false;
    } else {
        backImageField.style.display = 'block';
        backImageInput.required = true;
    }
}
```

## واجهة المشرف

### عرض الوثائق في لوحة المشرف

تم تخصيص لوحة المشرف لعرض:
- نوع الوثيقة
- الاسم الكامل كما يظهر في الوثيقة
- صور مصغرة للوثائق مع روابط للعرض الكامل
- حالة التحقق مع إمكانية إضافة ملاحظات

### الحقول المعروضة

```python
list_display = [
    'email', 'username', 'full_name_on_document', 'document_type', 
    'verification_status', 'date_joined', 'is_active', 
    'view_document_front', 'view_document_back', 'view_selfie_with_document'
]
```

## دليل المستخدم

### صفحة دليل التحقق (`/accounts/verification-guide/`)

تحتوي على:
- شرح أنواع الوثائق المقبولة
- تعليمات التصوير الصحيح
- أمثلة على الأخطاء الشائعة
- متطلبات صورة السيلفي
- معلومات عن عملية المراجعة

## الأمان والخصوصية

### حماية الصور
- جميع الصور محفوظة في مجلدات منفصلة
- الوصول للصور محمي عبر Django
- لا يمكن الوصول المباشر للصور من المتصفح

### التشفير
- جميع البيانات الحساسة مشفرة في قاعدة البيانات
- استخدام HTTPS في الإنتاج
- حماية CSRF في جميع النماذج

## عملية المراجعة

### خطوات المراجعة
1. المستخدم يرفع الوثائق والصور
2. المشرف يراجع الوثائق في لوحة المشرف
3. المشرف يتحقق من:
   - وضوح الصور
   - مطابقة الاسم
   - صحة الوثائق
   - جودة صورة السيلفي
4. المشرف يوافق أو يرفض مع إضافة ملاحظات

### حالات التحقق
- `pending`: في انتظار المراجعة
- `approved`: تم القبول
- `rejected`: تم الرفض
- `under_review`: قيد المراجعة

## التطوير المستقبلي

### تحسينات مقترحة
- [ ] إضافة تحقق تلقائي من صحة الوثائق باستخدام AI
- [ ] إضافة تحقق من تطابق الوجه في السيلفي مع الوثيقة
- [ ] إضافة نظام إشعارات للمستخدمين عند تغيير حالة التحقق
- [ ] إضافة إمكانية إعادة رفع الوثائق في حالة الرفض
- [ ] إضافة تقارير إحصائية لعمليات التحقق

## استكشاف الأخطاء

### مشاكل شائعة

1. **عدم ظهور حقل الصورة الخلفية**
   - تأكد من تشغيل JavaScript
   - تحقق من اختيار نوع الوثيقة

2. **فشل رفع الصور**
   - تأكد من إعدادات MEDIA_ROOT
   - تحقق من صلاحيات المجلدات

3. **عدم عرض الصور في لوحة المشرف**
   - تأكد من إعدادات MEDIA_URL
   - تحقق من مسار الصور

### سجلات الأخطاء

```python
# في settings.py
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': 'verification.log',
        },
    },
    'loggers': {
        'accounts.verification': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
```

## الخلاصة

نظام التحقق المتقدم يوفر:
- أمان عالي للمنصة
- مرونة في أنواع الوثائق
- سهولة في الاستخدام
- واجهة إدارية شاملة
- دليل مفصل للمستخدمين

هذا النظام يضمن أن جميع المستخدمين في المنصة موثقون ومتحققون، مما يزيد من الثقة والأمان في عمليات تداول العملات.
