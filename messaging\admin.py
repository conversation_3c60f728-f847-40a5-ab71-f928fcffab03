from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import Conversation, Message


class MessageInline(admin.TabularInline):
    """عرض الرسائل داخل المحادثة"""
    model = Message
    extra = 0
    readonly_fields = ['sent_at', 'read_at']
    fields = ['sender', 'content', 'is_read', 'sent_at', 'read_at']


@admin.register(Conversation)
class ConversationAdmin(admin.ModelAdmin):
    """
    إدارة المحادثات في لوحة المشرف
    """
    list_display = [
        'participant1', 'participant2', 'offer',
        'created_at', 'last_message_at', 'is_active'
    ]
    list_filter = ['is_active', 'created_at', 'offer__currency']
    search_fields = [
        'participant1__email', 'participant2__email',
        'offer__currency', 'offer__offer_type'
    ]
    ordering = ['-last_message_at']
    readonly_fields = ['created_at', 'last_message_at']

    inlines = [MessageInline]

    fieldsets = [
        (_('معلومات المحادثة'), {
            'fields': ('participant1', 'participant2', 'offer', 'is_active')
        }),
        (_('معلومات التوقيت'), {
            'fields': ('created_at', 'last_message_at'),
            'classes': ('collapse',)
        }),
    ]


@admin.register(Message)
class MessageAdmin(admin.ModelAdmin):
    """
    إدارة الرسائل في لوحة المشرف
    """
    list_display = [
        'sender', 'conversation', 'content_preview',
        'sent_at', 'is_read'
    ]
    list_filter = ['is_read', 'sent_at']
    search_fields = ['sender__email', 'content']
    ordering = ['-sent_at']
    readonly_fields = ['sent_at', 'read_at']

    def content_preview(self, obj):
        """معاينة محتوى الرسالة"""
        return obj.content[:50] + '...' if len(obj.content) > 50 else obj.content
    content_preview.short_description = _('معاينة المحتوى')
