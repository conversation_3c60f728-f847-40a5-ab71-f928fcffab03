{% extends 'base.html' %}

{% block title %}عروضي - تداول العملات{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="fas fa-user-circle me-2"></i>عروضي</h2>
            <p class="text-muted">إدارة عروضك للبيع والشراء</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{% url 'offers:create_offer' %}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                إنشاء عرض جديد
            </a>
        </div>
    </div>
    
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h4>{{ total_offers }}</h4>
                    <p class="mb-0">إجمالي العروض</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h4>{{ active_offers }}</h4>
                    <p class="mb-0">عروض نشطة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h4>{{ completed_offers }}</h4>
                    <p class="mb-0">عروض مكتملة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h4>{{ inactive_offers }}</h4>
                    <p class="mb-0">عروض متوقفة</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Offers List -->
    <div class="card">
        <div class="card-body">
            {% if offers %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>العملة</th>
                                <th>النوع</th>
                                <th>الكمية</th>
                                <th>السعر</th>
                                <th>المتبقي</th>
                                <th>الحالة</th>
                                <th>تاريخ النشر</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for offer in offers %}
                            <tr>
                                <td>
                                    <span class="badge currency-badge
                                        {% if offer.currency == 'USD' %}bg-success
                                        {% elif offer.currency == 'EUR' %}bg-primary
                                        {% else %}bg-secondary{% endif %}">
                                        {{ offer.get_currency_display }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge
                                        {% if offer.offer_type == 'buy' %}bg-info
                                        {% else %}bg-warning{% endif %}">
                                        {{ offer.get_offer_type_display }}
                                    </span>
                                </td>
                                <td>{{ offer.amount }}</td>
                                <td>{{ offer.price }} دج</td>
                                <td>
                                    {{ offer.remaining_amount }}
                                    {% if offer.completion_percentage > 0 %}
                                        <div class="progress mt-1" style="height: 5px;">
                                            <div class="progress-bar" style="width: {{ offer.completion_percentage }}%"></div>
                                        </div>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge
                                        {% if offer.status == 'active' %}bg-success
                                        {% elif offer.status == 'inactive' %}bg-secondary
                                        {% elif offer.status == 'completed' %}bg-primary
                                        {% else %}bg-danger{% endif %}">
                                        {{ offer.get_status_display }}
                                    </span>
                                </td>
                                <td>{{ offer.created_at|date:"d/m/Y" }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{% url 'offers:offer_detail' offer.pk %}" 
                                           class="btn btn-outline-primary" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'offers:edit_offer' offer.pk %}" 
                                           class="btn btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        {% if offer.status == 'active' %}
                                        <button class="btn btn-outline-secondary" 
                                                onclick="toggleOfferStatus({{ offer.pk }})" title="إيقاف">
                                            <i class="fas fa-pause"></i>
                                        </button>
                                        {% elif offer.status == 'inactive' %}
                                        <button class="btn btn-outline-success" 
                                                onclick="toggleOfferStatus({{ offer.pk }})" title="تفعيل">
                                            <i class="fas fa-play"></i>
                                        </button>
                                        {% endif %}
                                        <a href="{% url 'offers:delete_offer' offer.pk %}" 
                                           class="btn btn-outline-danger" title="حذف"
                                           onclick="return confirm('هل أنت متأكد من حذف هذا العرض؟')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                <nav aria-label="تصفح الصفحات" class="mt-3">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابق</a>
                            </li>
                        {% endif %}
                        
                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}">التالي</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-plus-circle fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد عروض بعد</h4>
                    <p class="text-muted">ابدأ بإنشاء أول عرض لك</p>
                    <a href="{% url 'offers:create_offer' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        إنشاء عرض جديد
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function toggleOfferStatus(offerId) {
    // سيتم إضافة AJAX لتغيير حالة العرض
    alert('سيتم إضافة هذه الميزة قريباً');
}
</script>
{% endblock %}
