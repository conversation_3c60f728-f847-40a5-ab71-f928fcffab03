{% extends 'base.html' %}

{% block title %}{{ offer.get_offer_type_display }} {{ offer.amount }} {{ offer.get_currency_display }} - تداول العملات{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-start mb-4">
                        <div>
                            <h2 class="card-title">
                                {{ offer.get_offer_type_display }} {{ offer.amount }} {{ offer.get_currency_display }}
                            </h2>
                            <p class="text-muted">
                                <i class="fas fa-user me-1"></i>
                                صاحب العرض: {{ offer.user.username }}
                            </p>
                        </div>
                        <div class="text-end">
                            <span class="badge currency-badge fs-6
                                {% if offer.currency == 'USD' %}bg-success
                                {% elif offer.currency == 'EUR' %}bg-primary
                                {% else %}bg-secondary{% endif %}">
                                {{ offer.get_currency_display }}
                            </span>
                            <br>
                            <span class="badge fs-6 mt-2
                                {% if offer.offer_type == 'buy' %}bg-info
                                {% else %}bg-warning{% endif %}">
                                {{ offer.get_offer_type_display }}
                            </span>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h4 class="text-primary">{{ offer.price }} دج</h4>
                                    <p class="mb-0">السعر لكل وحدة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h4 class="text-success">{{ offer.remaining_amount }}</h4>
                                    <p class="mb-0">الكمية المتبقية</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    {% if offer.notes %}
                    <div class="mb-4">
                        <h5><i class="fas fa-sticky-note me-1"></i>ملاحظات:</h5>
                        <div class="alert alert-light">
                            {{ offer.notes|linebreaks }}
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <p><strong>تاريخ النشر:</strong> {{ offer.created_at|date:"d/m/Y H:i" }}</p>
                            <p><strong>آخر تحديث:</strong> {{ offer.updated_at|date:"d/m/Y H:i" }}</p>
                        </div>
                        <div class="col-md-6">
                            {% if offer.expires_at %}
                                <p><strong>ينتهي في:</strong> {{ offer.expires_at|date:"d/m/Y H:i" }}</p>
                            {% endif %}
                            <p><strong>حالة العرض:</strong> 
                                <span class="badge 
                                    {% if offer.status == 'active' %}bg-success
                                    {% elif offer.status == 'inactive' %}bg-secondary
                                    {% elif offer.status == 'completed' %}bg-primary
                                    {% else %}bg-danger{% endif %}">
                                    {{ offer.get_status_display }}
                                </span>
                            </p>
                        </div>
                    </div>
                    
                    <!-- Progress Bar -->
                    {% if offer.completion_percentage > 0 %}
                    <div class="mb-4">
                        <label class="form-label">نسبة الإكمال:</label>
                        <div class="progress">
                            <div class="progress-bar" role="progressbar" 
                                 style="width: {{ offer.completion_percentage }}%"
                                 aria-valuenow="{{ offer.completion_percentage }}" 
                                 aria-valuemin="0" aria-valuemax="100">
                                {{ offer.completion_percentage|floatformat:1 }}%
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- Action Buttons -->
                    <div class="d-flex gap-2 flex-wrap">
                        {% if user == offer.user %}
                            <a href="{% url 'offers:edit_offer' offer.pk %}" class="btn btn-warning">
                                <i class="fas fa-edit me-1"></i>
                                تعديل العرض
                            </a>
                            <a href="{% url 'offers:delete_offer' offer.pk %}" class="btn btn-danger">
                                <i class="fas fa-trash me-1"></i>
                                حذف العرض
                            </a>
                        {% elif can_contact %}
                            <button class="btn btn-primary" onclick="alert('سيتم إضافة نظام الرسائل قريباً')">
                                <i class="fas fa-comments me-1"></i>
                                التواصل مع صاحب العرض
                            </button>
                        {% elif not user.is_authenticated %}
                            <a href="{% url 'accounts:login' %}" class="btn btn-primary">
                                <i class="fas fa-sign-in-alt me-1"></i>
                                سجل دخولك للتواصل
                            </a>
                        {% elif not user.is_verified %}
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                يجب أن يكون حسابك مقبولاً للتواصل مع أصحاب العروض
                            </div>
                        {% endif %}
                        
                        <a href="{% url 'offers:offer_list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-1"></i>
                            العودة للعروض
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-calculator me-1"></i>
                        حاسبة سريعة
                    </h5>
                    
                    <div class="mb-3">
                        <label class="form-label">الكمية:</label>
                        <input type="number" id="amount" class="form-control" 
                               placeholder="أدخل الكمية" step="0.01" min="0.01"
                               max="{{ offer.remaining_amount }}">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">المبلغ الإجمالي:</label>
                        <div class="input-group">
                            <input type="text" id="total" class="form-control" readonly>
                            <span class="input-group-text">دج</span>
                        </div>
                    </div>
                    
                    <small class="text-muted">
                        السعر: {{ offer.price }} دج لكل وحدة
                    </small>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-body">
                    <h6 class="card-title">معلومات صاحب العرض</h6>
                    <p><strong>اسم المستخدم:</strong> {{ offer.user.username }}</p>
                    <p><strong>عضو منذ:</strong> {{ offer.user.date_joined|date:"m/Y" }}</p>
                    <p><strong>حالة التحقق:</strong> 
                        {% if offer.user.is_verified %}
                            <span class="badge bg-success">مقبول</span>
                        {% else %}
                            <span class="badge bg-warning">غير مقبول</span>
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('amount').addEventListener('input', function() {
    const amount = parseFloat(this.value) || 0;
    const price = {{ offer.price }};
    const total = amount * price;
    document.getElementById('total').value = total.toFixed(2);
});
</script>
{% endblock %}
