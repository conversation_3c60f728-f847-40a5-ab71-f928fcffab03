from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import Offer


@admin.register(Offer)
class OfferAdmin(admin.ModelAdmin):
    """
    إدارة العروض في لوحة المشرف
    """
    list_display = [
        'user', 'currency', 'offer_type', 'amount',
        'remaining_amount', 'price', 'status', 'created_at'
    ]
    list_filter = ['currency', 'offer_type', 'status', 'created_at']
    search_fields = ['user__email', 'user__username', 'notes']
    ordering = ['-created_at']
    readonly_fields = ['created_at', 'updated_at', 'completion_percentage']

    fieldsets = [
        (_('معلومات العرض'), {
            'fields': ('user', 'currency', 'offer_type', 'amount', 'remaining_amount', 'price')
        }),
        (_('تفاصيل إضافية'), {
            'fields': ('notes', 'status', 'expires_at')
        }),
        (_('معلومات التوقيت'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    ]

    actions = ['activate_offers', 'deactivate_offers', 'cancel_offers']

    def activate_offers(self, request, queryset):
        """تفعيل العروض المحددة"""
        updated = queryset.update(status='active')
        self.message_user(request, f'تم تفعيل {updated} عرض.')
    activate_offers.short_description = _('تفعيل العروض المحددة')

    def deactivate_offers(self, request, queryset):
        """إلغاء تفعيل العروض المحددة"""
        updated = queryset.update(status='inactive')
        self.message_user(request, f'تم إلغاء تفعيل {updated} عرض.')
    deactivate_offers.short_description = _('إلغاء تفعيل العروض المحددة')

    def cancel_offers(self, request, queryset):
        """إلغاء العروض المحددة"""
        updated = queryset.update(status='cancelled')
        self.message_user(request, f'تم إلغاء {updated} عرض.')
    cancel_offers.short_description = _('إلغاء العروض المحددة')
