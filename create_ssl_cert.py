#!/usr/bin/env python3
"""
إنشاء شهادة SSL محلية للتطوير
"""

import os
import subprocess
import sys
from pathlib import Path

def create_ssl_certificate():
    """إنشاء شهادة SSL محلية"""
    
    # التحقق من وجود OpenSSL
    try:
        subprocess.run(['openssl', 'version'], check=True, capture_output=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ OpenSSL غير مثبت. جاري تثبيت البديل...")
        install_pyopenssl()
        return create_cert_with_python()
    
    # إنشاء الشهادة باستخدام OpenSSL
    return create_cert_with_openssl()

def install_pyopenssl():
    """تثبيت PyOpenSSL"""
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyopenssl'], check=True)
        print("✅ تم تثبيت PyOpenSSL بنجاح")
    except subprocess.CalledProcessError:
        print("❌ فشل في تثبيت PyOpenSSL")

def create_cert_with_openssl():
    """إنشاء الشهادة باستخدام OpenSSL"""
    try:
        # إنشاء المفتاح الخاص
        subprocess.run([
            'openssl', 'genrsa', '-out', 'server.key', '2048'
        ], check=True)
        
        # إنشاء الشهادة
        subprocess.run([
            'openssl', 'req', '-new', '-x509', '-key', 'server.key',
            '-out', 'server.crt', '-days', '365',
            '-subj', '/C=SA/ST=Riyadh/L=Riyadh/O=Dev/CN=***********'
        ], check=True)
        
        print("✅ تم إنشاء الشهادة بنجاح باستخدام OpenSSL")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في إنشاء الشهادة: {e}")
        return False

def create_cert_with_python():
    """إنشاء الشهادة باستخدام Python"""
    try:
        from OpenSSL import crypto
        import socket
        
        # إنشاء مفتاح
        key = crypto.PKey()
        key.generate_key(crypto.TYPE_RSA, 2048)
        
        # إنشاء الشهادة
        cert = crypto.X509()
        cert.get_subject().C = "SA"
        cert.get_subject().ST = "Riyadh"
        cert.get_subject().L = "Riyadh"
        cert.get_subject().O = "Development"
        cert.get_subject().CN = "***********"
        
        cert.set_serial_number(1000)
        cert.gmtime_adj_notBefore(0)
        cert.gmtime_adj_notAfter(365*24*60*60)  # سنة واحدة
        cert.set_issuer(cert.get_subject())
        cert.set_pubkey(key)
        cert.sign(key, 'sha256')
        
        # حفظ الملفات
        with open('server.crt', 'wb') as f:
            f.write(crypto.dump_certificate(crypto.FILETYPE_PEM, cert))
            
        with open('server.key', 'wb') as f:
            f.write(crypto.dump_privatekey(crypto.FILETYPE_PEM, key))
        
        print("✅ تم إنشاء الشهادة بنجاح باستخدام Python")
        return True
        
    except ImportError:
        print("❌ PyOpenSSL غير مثبت")
        return False
    except Exception as e:
        print(f"❌ فشل في إنشاء الشهادة: {e}")
        return False

def get_local_ip():
    """الحصول على IP المحلي"""
    try:
        import socket
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "***********"

if __name__ == "__main__":
    print("🔐 إنشاء شهادة SSL للتطوير...")
    
    local_ip = get_local_ip()
    print(f"📍 IP المحلي: {local_ip}")
    
    if create_ssl_certificate():
        print(f"""
✅ تم إنشاء الشهادة بنجاح!

📁 الملفات المنشأة:
- server.crt (الشهادة)
- server.key (المفتاح الخاص)

🚀 لتشغيل الخادم مع HTTPS:
python run_https_server.py

🌐 الروابط:
- للكمبيوتر: https://localhost:8000/
- للهاتف: https://{local_ip}:8000/

⚠️ ملاحظة: ستحتاج للموافقة على الشهادة في المتصفح
""")
    else:
        print("❌ فشل في إنشاء الشهادة")