# دليل تحويل التطبيق إلى APK

هذا الدليل يوضح كيفية تحويل تطبيق Django إلى تطبيق Android APK.

## الطرق المتاحة

### 1. استخد<PERSON>م WebView (الأسهل)
إنشاء تطبيق Android بسيط يعرض التطبيق في WebView.

#### المتطلبات:
- Android Studio
- معرفة أساسية بـ Java/Kotlin

#### الخطوات:
1. إنشاء مشروع Android جديد
2. إضافة WebView إلى التطبيق
3. توجيه WebView إلى عنوان الخادم
4. إضافة أذونات الإنترنت
5. بناء APK

### 2. استخدام Cordova/PhoneGap
تحويل التطبيق إلى تطبيق هجين.

#### التثبيت:
```bash
npm install -g cordova
cordova create CurrencyExchangeApp
cd CurrencyExchangeApp
cordova platform add android
```

#### الإعداد:
1. نسخ ملفات HTML/CSS/JS إلى مجلد www
2. تعديل config.xml
3. بناء التطبيق:
```bash
cordova build android
```

### 3. استخدام Kivy + Buildozer (للتطبيقات المحلية)
إنشاء تطبيق Python محلي.

#### التثبيت:
```bash
pip install kivy buildozer
```

#### إنشاء ملف buildozer.spec:
```ini
[app]
title = Currency Exchange
package.name = currencyexchange
package.domain = com.example

source.dir = .
source.include_exts = py,png,jpg,kv,atlas

version = 0.1
requirements = python3,kivy,requests

[buildozer]
log_level = 2

[app]
android.permissions = INTERNET,WRITE_EXTERNAL_STORAGE
```

### 4. استخدام React Native (الأكثر احترافية)
إعادة كتابة الواجهة الأمامية بـ React Native.

#### المتطلبات:
- Node.js
- React Native CLI
- Android Studio

## التوصية

للمشروع الحالي، ننصح باستخدام **WebView** لأنه:
- سريع التنفيذ
- يحافظ على جميع المميزات
- سهل الصيانة
- يدعم التحديثات التلقائية

## إعداد الخادم للموبايل

### 1. تحديث إعدادات Django
```python
# في settings.py
ALLOWED_HOSTS = ['*']  # أو عنوان IP محدد

# إضافة middleware للموبايل
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'django.middleware.locale.LocaleMiddleware',
]

# إعدادات الأمان للموبايل
X_FRAME_OPTIONS = 'ALLOWALL'  # للسماح بالعرض في WebView
```

### 2. إضافة CSS للموبايل
```css
/* في static/css/mobile.css */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .btn {
        padding: 12px;
        font-size: 16px;
    }
    
    .form-control {
        padding: 12px;
        font-size: 16px;
    }
}
```

### 3. إضافة Meta Tags للموبايل
```html
<!-- في templates/base.html -->
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="black">
```

## مثال WebView Android

### MainActivity.java
```java
package com.example.currencyexchange;

import android.app.Activity;
import android.os.Bundle;
import android.webkit.WebView;
import android.webkit.WebViewClient;

public class MainActivity extends Activity {
    private WebView webView;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        webView = findViewById(R.id.webview);
        webView.setWebViewClient(new WebViewClient());
        webView.getSettings().setJavaScriptEnabled(true);
        webView.getSettings().setDomStorageEnabled(true);
        
        // تحميل التطبيق
        webView.loadUrl("http://YOUR_SERVER_IP:8000");
    }

    @Override
    public void onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack();
        } else {
            super.onBackPressed();
        }
    }
}
```

### activity_main.xml
```xml
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <WebView
        android:id="@+id/webview"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</LinearLayout>
```

### AndroidManifest.xml
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
```

## الخطوات التالية

1. اختيار الطريقة المناسبة
2. إعداد بيئة التطوير
3. تطوير التطبيق
4. اختبار التطبيق
5. نشر التطبيق على Google Play Store

## ملاحظات مهمة

- تأكد من أن الخادم متاح على الإنترنت
- استخدم HTTPS في الإنتاج
- اختبر التطبيق على أجهزة مختلفة
- راعي أحجام الشاشات المختلفة
- أضف أيقونة وشاشة بداية للتطبيق
