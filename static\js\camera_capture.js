// متغيرات عامة
let currentStep = 1;
let totalSteps = 6;
let selectedDocumentType = '';
let mediaStream = null;
let mediaRecorder = null;
let recordedChunks = [];
let recordingTimer = 0;
let timerInterval = null;

// بيانات الصور والفيديو
let capturedData = {
    documentType: '',
    frontImage: null,
    backImage: null,
    selfieImage: null,
    faceVideo: null
};

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    setupEventListeners();
    checkCameraSupport();
});

function initializePage() {
    updateProgress();
    updateStepDisplay();
}

// فحص دعم الكاميرا عند تحميل الصفحة
async function checkCameraSupport() {
    const supportInfo = document.createElement('div');
    supportInfo.className = 'alert alert-info mb-3';
    supportInfo.innerHTML = `
        <div class="d-flex align-items-center">
            <div class="spinner-border spinner-border-sm me-2" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <span>جاري فحص دعم الكاميرا...</span>
        </div>
    `;
    
    const step1 = document.getElementById('step-1');
    step1.insertBefore(supportInfo, step1.firstChild);
    
    try {
        // التحقق من دعم المتصفح
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            throw new Error('المتصفح لا يدعم الوصول للكاميرا');
        }
        
        // التحقق من الأذونات
        let permissionStatus = 'prompt';
        try {
            const permission = await navigator.permissions.query({name: 'camera'});
            permissionStatus = permission.state;
        } catch (e) {
            console.log('لا يمكن فحص حالة الإذن:', e);
        }
        
        // عرض حالة الدعم
        let statusMessage = '';
        let statusClass = '';
        
        if (permissionStatus === 'granted') {
            statusMessage = '<i class="fas fa-check-circle me-2"></i>الكاميرا مدعومة ومسموحة. يمكنك البدء مباشرة!';
            statusClass = 'alert-success';
            enableDocumentSelection();
        } else if (permissionStatus === 'denied') {
            statusMessage = '<i class="fas fa-times-circle me-2"></i>تم رفض إذن الكاميرا. يرجى تغيير الإعدادات للمتابعة.';
            statusClass = 'alert-warning';
            // إظهار زر طلب الإذن
            document.getElementById('request-permission').style.display = 'inline-block';
        } else {
            statusMessage = '<i class="fas fa-info-circle me-2"></i>الكاميرا مدعومة. اضغط على "اختبار الكاميرا" للبدء.';
            statusClass = 'alert-info';
            // إظهار زر طلب الإذن
            document.getElementById('request-permission').style.display = 'inline-block';
        }
        
        supportInfo.className = `alert ${statusClass} mb-3`;
        supportInfo.innerHTML = statusMessage;
        
    } catch (error) {
        console.error('خطأ في فحص دعم الكاميرا:', error);
        
        let errorMessage = '';
        if (error.message.includes('لا يدعم')) {
            errorMessage = '<i class="fas fa-exclamation-triangle me-2"></i>المتصفح لا يدعم الكاميرا. جرب Chrome أو Firefox.';
        } else {
            errorMessage = '<i class="fas fa-exclamation-triangle me-2"></i>مشكلة في فحص الكاميرا. جرب إعادة تحميل الصفحة.';
        }
        
        supportInfo.className = 'alert alert-danger mb-3';
        supportInfo.innerHTML = errorMessage;
    }
}

function setupEventListeners() {
    // اختيار نوع الوثيقة
    document.querySelectorAll('.document-type-card').forEach(card => {
        card.addEventListener('click', function() {
            selectDocumentType(this.dataset.type);
        });
    });

    // أزرار التنقل
    document.getElementById('prev-step').addEventListener('click', previousStep);
    document.getElementById('next-step').addEventListener('click', nextStep);

    // أزرار التصوير
    document.getElementById('capture-front').addEventListener('click', () => capturePhoto('front'));
    document.getElementById('capture-back').addEventListener('click', () => capturePhoto('back'));
    document.getElementById('capture-selfie').addEventListener('click', () => capturePhoto('selfie'));

    // أزرار إعادة التصوير
    document.getElementById('retake-front').addEventListener('click', () => retakePhoto('front'));
    document.getElementById('retake-back').addEventListener('click', () => retakePhoto('back'));
    document.getElementById('retake-selfie').addEventListener('click', () => retakePhoto('selfie'));

    // أزرار التأكيد
    document.getElementById('confirm-front').addEventListener('click', () => confirmPhoto('front'));
    document.getElementById('confirm-back').addEventListener('click', () => confirmPhoto('back'));
    document.getElementById('confirm-selfie').addEventListener('click', () => confirmPhoto('selfie'));

    // أزرار الفيديو
    document.getElementById('start-recording').addEventListener('click', startRecording);
    document.getElementById('stop-recording').addEventListener('click', stopRecording);
    document.getElementById('retake-video').addEventListener('click', retakeVideo);
    document.getElementById('confirm-video').addEventListener('click', confirmVideo);

    // نموذج التسجيل
    document.getElementById('registration-form').addEventListener('submit', submitRegistration);

    // اختبار الكاميرا
    document.getElementById('test-camera').addEventListener('click', testCamera);
    
    // طلب إذن الكاميرا
    document.getElementById('request-permission').addEventListener('click', requestCameraPermission);
}

async function testCamera() {
    const button = document.getElementById('test-camera');
    const resultDiv = document.getElementById('camera-test-result');

    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الاختبار...';

    try {
        // التحقق من دعم المتصفح
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            throw new Error('المتصفح لا يدعم الوصول للكاميرا');
        }

        // عرض رسالة طلب الإذن
        resultDiv.innerHTML = `
            <div class="alert alert-info mt-2">
                <i class="fas fa-info-circle me-2"></i>
                سيطلب المتصفح إذن الوصول للكاميرا. يرجى الضغط على "السماح" أو "Allow".
            </div>
        `;

        // طلب الوصول للكاميرا مع إعدادات محسنة
        const stream = await navigator.mediaDevices.getUserMedia({
            video: {
                facingMode: 'user',
                width: { ideal: 1280, min: 640 },
                height: { ideal: 720, min: 480 }
            },
            audio: false
        });

        // إيقاف الكاميرا فوراً
        stream.getTracks().forEach(track => track.stop());

        // عرض رسالة نجاح
        resultDiv.innerHTML = `
            <div class="alert alert-success mt-2">
                <i class="fas fa-check-circle me-2"></i>
                ممتاز! تم منح الإذن بنجاح. الكاميرا تعمل بشكل صحيح ويمكنك المتابعة.
            </div>
        `;

        button.innerHTML = '<i class="fas fa-check me-2"></i>الكاميرا تعمل';
        button.className = 'btn btn-success';

        // تمكين المتابعة للخطوة التالية
        enableDocumentSelection();

    } catch (error) {
        console.error('خطأ في اختبار الكاميرا:', error);

        let errorMessage = '';
        let instructions = '';

        if (error.name === 'NotAllowedError') {
            errorMessage = 'تم رفض الإذن للوصول للكاميرا';
            instructions = `
                <strong>كيفية السماح للكاميرا:</strong><br>
                <div class="mt-2">
                    <strong>في Chrome:</strong><br>
                    1. انقر على أيقونة القفل 🔒 في شريط العنوان<br>
                    2. اختر "السماح" للكاميرا<br>
                    3. أعد تحميل الصفحة<br><br>
                    
                    <strong>في Firefox:</strong><br>
                    1. انقر على أيقونة الكاميرا في شريط العنوان<br>
                    2. اختر "السماح"<br>
                    3. أعد تحميل الصفحة<br><br>
                    
                    <strong>في Safari:</strong><br>
                    1. اذهب إلى Safari > الإعدادات > مواقع الويب<br>
                    2. اختر "الكاميرا" واسمح لهذا الموقع
                </div>
            `;
        } else if (error.name === 'NotFoundError') {
            errorMessage = 'لم يتم العثور على كاميرا في الجهاز';
            instructions = `
                <strong>تحقق من:</strong><br>
                • وجود كاميرا متصلة بالجهاز<br>
                • عدم استخدام الكاميرا من تطبيق آخر<br>
                • تشغيل الكاميرا في إعدادات النظام
            `;
        } else if (error.name === 'NotSupportedError') {
            errorMessage = 'المتصفح لا يدعم الوصول للكاميرا عبر HTTP';
            instructions = `
                <strong>الحلول:</strong><br>
                • استخدم HTTPS بدلاً من HTTP<br>
                • جرب على localhost<br>
                • استخدم متصفح حديث (Chrome, Firefox, Safari)
            `;
        } else if (error.name === 'NotReadableError') {
            errorMessage = 'الكاميرا مستخدمة من تطبيق آخر';
            instructions = `
                <strong>الحلول:</strong><br>
                • أغلق التطبيقات الأخرى التي تستخدم الكاميرا<br>
                • أعد تشغيل المتصفح<br>
                • أعد تشغيل الجهاز إذا لزم الأمر
            `;
        } else {
            errorMessage = error.message || 'خطأ غير معروف';
            instructions = `
                <strong>جرب:</strong><br>
                • إعادة تحميل الصفحة<br>
                • استخدام متصفح آخر<br>
                • التحقق من إعدادات الكاميرا في النظام
            `;
        }

        resultDiv.innerHTML = `
            <div class="alert alert-danger mt-2">
                <h6><i class="fas fa-exclamation-triangle me-2"></i>مشكلة في الوصول للكاميرا</h6>
                <p><strong>الخطأ:</strong> ${errorMessage}</p>
                <div class="mt-3">
                    ${instructions}
                </div>
                <div class="mt-3">
                    <button class="btn btn-primary btn-sm" onclick="location.reload()">
                        <i class="fas fa-refresh me-1"></i>إعادة تحميل الصفحة
                    </button>
                </div>
            </div>
        `;

        button.innerHTML = '<i class="fas fa-times me-2"></i>فشل الاختبار - أعد المحاولة';
        button.className = 'btn btn-danger';
    }

    button.disabled = false;
}

// دالة لتمكين اختيار نوع الوثيقة بعد نجاح اختبار الكاميرا
function enableDocumentSelection() {
    document.querySelectorAll('.document-type-card').forEach(card => {
        card.style.opacity = '1';
        card.style.pointerEvents = 'auto';
        card.classList.add('camera-ready');
    });
}

// دالة لطلب إذن الكاميرا مباشرة
async function requestCameraPermission() {
    const button = document.getElementById('request-permission');
    const resultDiv = document.getElementById('camera-test-result');
    
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري طلب الإذن...';
    
    try {
        // عرض رسالة توضيحية
        resultDiv.innerHTML = `
            <div class="alert alert-info mt-2">
                <i class="fas fa-info-circle me-2"></i>
                <strong>سيظهر طلب إذن الكاميرا الآن</strong><br>
                يرجى الضغط على "السماح" أو "Allow" عندما يظهر الطلب في أعلى المتصفح.
            </div>
        `;
        
        // طلب الوصول للكاميرا
        const stream = await navigator.mediaDevices.getUserMedia({
            video: {
                facingMode: 'user',
                width: { ideal: 1280, min: 640 },
                height: { ideal: 720, min: 480 }
            },
            audio: false
        });
        
        // إيقاف الكاميرا فوراً
        stream.getTracks().forEach(track => track.stop());
        
        // عرض رسالة نجاح
        resultDiv.innerHTML = `
            <div class="alert alert-success mt-2">
                <i class="fas fa-check-circle me-2"></i>
                <strong>تم منح الإذن بنجاح!</strong><br>
                يمكنك الآن اختيار نوع الوثيقة والمتابعة.
            </div>
        `;
        
        button.innerHTML = '<i class="fas fa-check me-2"></i>تم منح الإذن';
        button.className = 'btn btn-success';
        
        // تمكين اختيار الوثائق
        enableDocumentSelection();
        
        // إخفاء زر طلب الإذن
        button.style.display = 'none';
        
    } catch (error) {
        console.error('خطأ في طلب إذن الكاميرا:', error);
        
        let errorMessage = '';
        let instructions = '';
        
        if (error.name === 'NotAllowedError') {
            errorMessage = 'تم رفض الإذن للوصول للكاميرا';
            instructions = `
                <div class="mt-3">
                    <strong>كيفية السماح للكاميرا:</strong><br>
                    <div class="row mt-2">
                        <div class="col-md-6">
                            <strong>في Chrome:</strong><br>
                            1. انقر على أيقونة القفل 🔒 في شريط العنوان<br>
                            2. اختر "السماح" للكاميرا<br>
                            3. أعد تحميل الصفحة
                        </div>
                        <div class="col-md-6">
                            <strong>في Firefox:</strong><br>
                            1. انقر على أيقونة الكاميرا في شريط العنوان<br>
                            2. اختر "السماح"<br>
                            3. أعد تحميل الصفحة
                        </div>
                    </div>
                </div>
            `;
        } else if (error.name === 'NotFoundError') {
            errorMessage = 'لم يتم العثور على كاميرا في الجهاز';
            instructions = `
                <div class="mt-3">
                    <strong>تحقق من:</strong><br>
                    • وجود كاميرا متصلة بالجهاز<br>
                    • عدم استخدام الكاميرا من تطبيق آخر<br>
                    • تشغيل الكاميرا في إعدادات النظام
                </div>
            `;
        } else {
            errorMessage = error.message || 'خطأ غير معروف';
            instructions = `
                <div class="mt-3">
                    <strong>جرب:</strong><br>
                    • إعادة تحميل الصفحة<br>
                    • استخدام متصفح آخر<br>
                    • التحقق من إعدادات الكاميرا
                </div>
            `;
        }
        
        resultDiv.innerHTML = `
            <div class="alert alert-danger mt-2">
                <h6><i class="fas fa-exclamation-triangle me-2"></i>فشل في الحصول على الإذن</h6>
                <p><strong>الخطأ:</strong> ${errorMessage}</p>
                ${instructions}
                <div class="mt-3">
                    <button class="btn btn-primary btn-sm" onclick="location.reload()">
                        <i class="fas fa-refresh me-1"></i>إعادة تحميل الصفحة
                    </button>
                </div>
            </div>
        `;
        
        button.innerHTML = '<i class="fas fa-times me-2"></i>فشل - أعد المحاولة';
        button.className = 'btn btn-danger';
    }
    
    button.disabled = false;
}

function selectDocumentType(type) {
    selectedDocumentType = type;
    capturedData.documentType = type;
    
    // تحديث العرض
    document.querySelectorAll('.document-type-card').forEach(card => {
        card.classList.remove('selected');
    });
    document.querySelector(`[data-type="${type}"]`).classList.add('selected');
    
    // تحديد عدد الخطوات حسب نوع الوثيقة
    if (type === 'passport') {
        totalSteps = 5; // بدون الوجه الخلفي
    } else {
        totalSteps = 6;
    }
    
    // إظهار زر التالي
    document.getElementById('next-step').style.display = 'block';
}

function updateProgress() {
    const percentage = (currentStep / totalSteps) * 100;
    document.getElementById('progress-bar').style.width = percentage + '%';
    document.getElementById('progress-text').textContent = Math.round(percentage) + '%';
}

function updateStepDisplay() {
    // إخفاء جميع الخطوات
    document.querySelectorAll('.step-content').forEach(step => {
        step.classList.add('d-none');
    });
    
    // إظهار الخطوة الحالية
    document.getElementById(`step-${currentStep}`).classList.remove('d-none');
    
    // تحديث العنوان والوصف
    const titles = {
        1: 'الخطوة 1: اختيار نوع الوثيقة',
        2: 'الخطوة 2: تصوير الوثيقة - الوجه الأمامي',
        3: 'الخطوة 3: تصوير الوثيقة - الوجه الخلفي',
        4: 'الخطوة 4: تصوير السيلفي مع الوثيقة',
        5: 'الخطوة 5: تسجيل فيديو الوجه',
        6: 'الخطوة 6: إكمال بيانات التسجيل'
    };
    
    const descriptions = {
        1: 'اختر نوع الوثيقة التي تريد تصويرها',
        2: 'صور الوجه الأمامي للوثيقة بوضوح',
        3: 'صور الوجه الخلفي للوثيقة',
        4: 'التقط صورة سيلفي وأنت تحمل الوثيقة',
        5: 'سجل فيديو قصير لوجهك للتحقق من الهوية',
        6: 'أدخل بياناتك الشخصية لإكمال التسجيل'
    };
    
    document.getElementById('step-title').textContent = titles[currentStep];
    document.getElementById('step-description').textContent = descriptions[currentStep];
    
    // تحديث أزرار التنقل
    document.getElementById('prev-step').style.display = currentStep > 1 ? 'block' : 'none';
    document.getElementById('next-step').style.display = 'none'; // سيتم إظهاره حسب الحاجة
}

function nextStep() {
    if (currentStep < totalSteps) {
        currentStep++;
        
        // تخطي الخطوة 3 للجواز
        if (currentStep === 3 && selectedDocumentType === 'passport') {
            currentStep++;
        }
        
        updateProgress();
        updateStepDisplay();
        
        // بدء الكاميرا للخطوات التي تحتاجها
        if (currentStep === 2) {
            startCamera('camera-video');
        } else if (currentStep === 3) {
            startCamera('camera-video-back');
        } else if (currentStep === 4) {
            startCamera('camera-video-selfie');
        } else if (currentStep === 5) {
            startCamera('camera-video-face');
        } else if (currentStep === 6) {
            stopAllCameras();
            prepareRegistrationForm();
        }
    }
}

function previousStep() {
    if (currentStep > 1) {
        currentStep--;
        
        // تخطي الخطوة 3 للجواز
        if (currentStep === 3 && selectedDocumentType === 'passport') {
            currentStep--;
        }
        
        updateProgress();
        updateStepDisplay();
        
        // إيقاف الكاميرا
        stopAllCameras();
    }
}

async function startCamera(videoElementId) {
    try {
        // التحقق من دعم المتصفح للكاميرا
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            throw new Error('المتصفح لا يدعم الوصول للكاميرا');
        }

        // إيقاف أي كاميرا سابقة
        stopAllCameras();

        // عرض رسالة تحميل
        showCameraLoading(videoElementId);

        // تحديد نوع الكاميرا المطلوبة
        const facingMode = videoElementId.includes('selfie') || videoElementId.includes('face') ? 'user' : 'environment';
        
        const constraints = {
            video: {
                facingMode: facingMode,
                width: { ideal: 1280, min: 640 },
                height: { ideal: 720, min: 480 }
            },
            audio: false
        };

        console.log('محاولة الوصول للكاميرا مع القيود:', constraints);
        
        // طلب الوصول للكاميرا
        mediaStream = await navigator.mediaDevices.getUserMedia(constraints);

        const videoElement = document.getElementById(videoElementId);
        if (videoElement) {
            videoElement.srcObject = mediaStream;
            console.log('تم تشغيل الكاميرا بنجاح');

            // إخفاء رسالة التحميل
            hideCameraLoading(videoElementId);

            // التأكد من تشغيل الفيديو
            videoElement.onloadedmetadata = function() {
                videoElement.play().catch(e => {
                    console.error('خطأ في تشغيل الفيديو:', e);
                });
            };

            // إضافة مستمع لأخطاء الفيديو
            videoElement.onerror = function(e) {
                console.error('خطأ في عنصر الفيديو:', e);
                showCameraError('خطأ في عرض الفيديو', videoElementId);
            };
        }
    } catch (error) {
        console.error('خطأ في الوصول للكاميرا:', error);
        hideCameraLoading(videoElementId);

        let errorMessage = '';
        let userInstructions = '';

        if (error.name === 'NotAllowedError') {
            errorMessage = 'تم رفض الإذن للوصول للكاميرا';
            userInstructions = 'يرجى السماح للموقع بالوصول للكاميرا من إعدادات المتصفح، ثم أعد تحميل الصفحة.';
        } else if (error.name === 'NotFoundError') {
            errorMessage = 'لم يتم العثور على كاميرا في الجهاز';
            userInstructions = 'تأكد من وجود كاميرا متصلة بالجهاز وأنها تعمل بشكل صحيح.';
        } else if (error.name === 'NotSupportedError') {
            errorMessage = 'المتصفح لا يدعم الوصول للكاميرا';
            userInstructions = 'يرجى استخدام متصفح حديث مثل Chrome أو Firefox، أو تأكد من استخدام HTTPS.';
        } else if (error.name === 'NotReadableError') {
            errorMessage = 'الكاميرا مستخدمة من تطبيق آخر';
            userInstructions = 'أغلق التطبيقات الأخرى التي تستخدم الكاميرا وأعد المحاولة.';
        } else {
            errorMessage = error.message || 'خطأ غير معروف في الكاميرا';
            userInstructions = 'جرب إعادة تحميل الصفحة أو استخدام متصفح آخر.';
        }

        // عرض رسالة الخطأ
        showCameraError(errorMessage, videoElementId, userInstructions);
    }
}

// دالة لعرض رسالة التحميل
function showCameraLoading(videoElementId) {
    const videoElement = document.getElementById(videoElementId);
    if (videoElement && videoElement.parentElement) {
        const loadingDiv = document.createElement('div');
        loadingDiv.className = 'camera-loading-overlay';
        loadingDiv.innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="text-muted">جاري تشغيل الكاميرا...</p>
                <small class="text-muted">إذا ظهرت رسالة طلب الإذن، يرجى الضغط على "السماح"</small>
            </div>
        `;
        loadingDiv.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        `;
        
        videoElement.parentElement.style.position = 'relative';
        videoElement.parentElement.appendChild(loadingDiv);
    }
}

// دالة لإخفاء رسالة التحميل
function hideCameraLoading(videoElementId) {
    const videoElement = document.getElementById(videoElementId);
    if (videoElement && videoElement.parentElement) {
        const loadingOverlay = videoElement.parentElement.querySelector('.camera-loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.remove();
        }
    }
}

// دالة لعرض رسالة خطأ الكاميرا
function showCameraError(errorMessage, videoElementId, instructions = '') {
    const videoElement = document.getElementById(videoElementId);
    if (videoElement && videoElement.parentElement) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'camera-error-overlay';
        errorDiv.innerHTML = `
            <div class="alert alert-danger text-center">
                <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                <h6>مشكلة في الكاميرا</h6>
                <p><strong>${errorMessage}</strong></p>
                ${instructions ? `<p class="small">${instructions}</p>` : ''}
                <button class="btn btn-primary btn-sm mt-2" onclick="location.reload()">
                    <i class="fas fa-refresh me-1"></i>إعادة تحميل الصفحة
                </button>
            </div>
        `;
        errorDiv.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.95);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
            padding: 20px;
        `;
        
        videoElement.parentElement.style.position = 'relative';
        
        // إزالة أي رسالة خطأ سابقة
        const existingError = videoElement.parentElement.querySelector('.camera-error-overlay');
        if (existingError) {
            existingError.remove();
        }
        
        videoElement.parentElement.appendChild(errorDiv);
    }
}

function stopAllCameras() {
    if (mediaStream) {
        mediaStream.getTracks().forEach(track => track.stop());
        mediaStream = null;
    }
}

function capturePhoto(type) {
    const videoId = type === 'front' ? 'camera-video' : 
                   type === 'back' ? 'camera-video-back' : 'camera-video-selfie';
    const canvasId = type === 'front' ? 'photo-canvas' : 
                    type === 'back' ? 'photo-canvas-back' : 'photo-canvas-selfie';
    const previewId = type === 'front' ? 'front-preview' : 
                     type === 'back' ? 'back-preview' : 'selfie-preview';
    
    const video = document.getElementById(videoId);
    const canvas = document.getElementById(canvasId);
    const preview = document.getElementById(previewId);
    
    // تعيين أبعاد الكانفاس
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    
    // رسم الصورة على الكانفاس
    const ctx = canvas.getContext('2d');
    ctx.drawImage(video, 0, 0);
    
    // تحويل إلى base64
    const imageData = canvas.toDataURL('image/jpeg', 0.8);
    
    // حفظ البيانات
    if (type === 'front') {
        capturedData.frontImage = imageData;
    } else if (type === 'back') {
        capturedData.backImage = imageData;
    } else {
        capturedData.selfieImage = imageData;
    }
    
    // عرض المعاينة
    preview.src = imageData;
    preview.style.display = 'block';
    preview.parentElement.querySelector('.preview-placeholder').style.display = 'none';
    
    // إظهار أزرار إعادة التصوير والتأكيد
    document.getElementById(`retake-${type}`).style.display = 'inline-block';
    document.getElementById(`confirm-${type}`).style.display = 'inline-block';
}

function retakePhoto(type) {
    const previewId = type === 'front' ? 'front-preview' : 
                     type === 'back' ? 'back-preview' : 'selfie-preview';
    const preview = document.getElementById(previewId);
    
    // إخفاء المعاينة
    preview.style.display = 'none';
    preview.parentElement.querySelector('.preview-placeholder').style.display = 'block';
    
    // إخفاء الأزرار
    document.getElementById(`retake-${type}`).style.display = 'none';
    document.getElementById(`confirm-${type}`).style.display = 'none';
    
    // مسح البيانات
    if (type === 'front') {
        capturedData.frontImage = null;
    } else if (type === 'back') {
        capturedData.backImage = null;
    } else {
        capturedData.selfieImage = null;
    }
}

function confirmPhoto(type) {
    // إظهار زر التالي
    document.getElementById('next-step').style.display = 'block';
}

async function startRecording() {
    try {
        const stream = document.getElementById('camera-video-face').srcObject;
        
        mediaRecorder = new MediaRecorder(stream, {
            mimeType: 'video/webm;codecs=vp8'
        });
        
        recordedChunks = [];
        
        mediaRecorder.ondataavailable = function(event) {
            if (event.data.size > 0) {
                recordedChunks.push(event.data);
            }
        };
        
        mediaRecorder.onstop = function() {
            const blob = new Blob(recordedChunks, { type: 'video/webm' });
            const videoURL = URL.createObjectURL(blob);
            
            // عرض المعاينة
            const preview = document.getElementById('face-video-preview');
            preview.src = videoURL;
            preview.style.display = 'block';
            preview.parentElement.querySelector('.preview-placeholder').style.display = 'none';
            
            // حفظ البيانات
            const reader = new FileReader();
            reader.onload = function() {
                capturedData.faceVideo = reader.result;
            };
            reader.readAsDataURL(blob);
            
            // إظهار أزرار إعادة التسجيل والتأكيد
            document.getElementById('retake-video').style.display = 'inline-block';
            document.getElementById('confirm-video').style.display = 'inline-block';
        };
        
        mediaRecorder.start();
        
        // تحديث واجهة المستخدم
        document.getElementById('start-recording').style.display = 'none';
        document.getElementById('stop-recording').style.display = 'inline-block';
        document.getElementById('recording-timer').style.display = 'block';
        
        // بدء العداد
        recordingTimer = 0;
        timerInterval = setInterval(() => {
            recordingTimer++;
            document.getElementById('timer').textContent = recordingTimer;
            
            // إيقاف التسجيل تلقائياً بعد 10 ثواني
            if (recordingTimer >= 10) {
                stopRecording();
            }
        }, 1000);
        
    } catch (error) {
        console.error('خطأ في بدء التسجيل:', error);
        alert('حدث خطأ في بدء تسجيل الفيديو');
    }
}

function stopRecording() {
    if (mediaRecorder && mediaRecorder.state === 'recording') {
        mediaRecorder.stop();
    }
    
    // إيقاف العداد
    if (timerInterval) {
        clearInterval(timerInterval);
        timerInterval = null;
    }
    
    // تحديث واجهة المستخدم
    document.getElementById('start-recording').style.display = 'inline-block';
    document.getElementById('stop-recording').style.display = 'none';
    document.getElementById('recording-timer').style.display = 'none';
}

function retakeVideo() {
    // إخفاء المعاينة
    const preview = document.getElementById('face-video-preview');
    preview.style.display = 'none';
    preview.parentElement.querySelector('.preview-placeholder').style.display = 'block';
    
    // إخفاء الأزرار
    document.getElementById('retake-video').style.display = 'none';
    document.getElementById('confirm-video').style.display = 'none';
    
    // مسح البيانات
    capturedData.faceVideo = null;
    recordedChunks = [];
}

function confirmVideo() {
    // إظهار زر التالي
    document.getElementById('next-step').style.display = 'block';
}

function prepareRegistrationForm() {
    // ملء الحقول المخفية
    document.getElementById('document_type').value = capturedData.documentType;
    document.getElementById('document_front_data').value = capturedData.frontImage;
    document.getElementById('document_back_data').value = capturedData.backImage || '';
    document.getElementById('selfie_data').value = capturedData.selfieImage;
    document.getElementById('face_video_data').value = capturedData.faceVideo;
}

function submitRegistration(event) {
    event.preventDefault();
    
    // التحقق من اكتمال البيانات
    if (!capturedData.frontImage || !capturedData.selfieImage || !capturedData.faceVideo) {
        alert('يرجى إكمال جميع خطوات التصوير قبل التسجيل');
        return;
    }
    
    if (capturedData.documentType !== 'passport' && !capturedData.backImage) {
        alert('يرجى تصوير الوجه الخلفي للوثيقة');
        return;
    }
    
    // إرسال النموذج
    const formData = new FormData(event.target);
    
    // إضافة الصور والفيديو كملفات
    if (capturedData.frontImage) {
        const frontBlob = dataURLtoBlob(capturedData.frontImage);
        formData.append('document_front_image', frontBlob, 'front.jpg');
    }
    
    if (capturedData.backImage) {
        const backBlob = dataURLtoBlob(capturedData.backImage);
        formData.append('document_back_image', backBlob, 'back.jpg');
    }
    
    if (capturedData.selfieImage) {
        const selfieBlob = dataURLtoBlob(capturedData.selfieImage);
        formData.append('selfie_with_document', selfieBlob, 'selfie.jpg');
    }
    
    if (capturedData.faceVideo) {
        const videoBlob = dataURLtoBlob(capturedData.faceVideo);
        formData.append('face_verification_video', videoBlob, 'face_video.webm');
    }
    
    // إرسال البيانات
    fetch(window.location.href, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => {
        if (response.ok) {
            window.location.href = '/accounts/registration-success/';
        } else {
            throw new Error('فشل في التسجيل');
        }
    })
    .catch(error => {
        console.error('خطأ في التسجيل:', error);
        alert('حدث خطأ في التسجيل. يرجى المحاولة مرة أخرى.');
    });
}

// دالة مساعدة لتحويل data URL إلى Blob
function dataURLtoBlob(dataURL) {
    const arr = dataURL.split(',');
    const mime = arr[0].match(/:(.*?);/)[1];
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
    }
    return new Blob([u8arr], { type: mime });
}

// عرض رسالة خطأ الكاميرا
function showCameraError(message) {
    const currentStepElement = document.getElementById(`step-${currentStep}`);
    if (currentStepElement) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-danger mt-3';
        errorDiv.innerHTML = `
            <h5><i class="fas fa-exclamation-triangle me-2"></i>مشكلة في الكاميرا</h5>
            <p>${message}</p>
            <hr>
            <h6>الحلول المقترحة:</h6>
            <ul>
                <li>تأكد من السماح للموقع بالوصول للكاميرا</li>
                <li>جرب متصفح آخر (Chrome موصى به)</li>
                <li>تأكد أن الكاميرا تعمل في تطبيقات أخرى</li>
                <li>أعد تحميل الصفحة وجرب مرة أخرى</li>
            </ul>
            <button class="btn btn-primary" onclick="location.reload()">
                <i class="fas fa-redo me-2"></i>إعادة المحاولة
            </button>
        `;

        // إزالة أي رسائل خطأ سابقة
        const existingError = currentStepElement.querySelector('.alert-danger');
        if (existingError) {
            existingError.remove();
        }

        currentStepElement.appendChild(errorDiv);
    }
}
