plugins {
    id 'com.android.application'
}

android {
    namespace 'com.currencyexchange.app'
    compileSdk 34

    defaultConfig {
        applicationId "com.currencyexchange.app"
        minSdk 21
        targetSdk 34
        versionCode 1
        versionName "1.0.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        
        // دعم اللغة العربية
        resConfigs "ar", "en"
        
        // إعدادات ProGuard
        proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
    }

    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            
            // إعدادات التوقيع
            signingConfig signingConfigs.debug
        }
        debug {
            minifyEnabled false
            debuggable true
            applicationIdSuffix ".debug"
            versionNameSuffix "-debug"
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    buildFeatures {
        viewBinding true
        dataBinding true
    }

    packagingOptions {
        resources {
            excludes += ['META-INF/DEPENDENCIES', 'META-INF/LICENSE', 'META-INF/LICENSE.txt', 'META-INF/license.txt', 'META-INF/NOTICE', 'META-INF/NOTICE.txt', 'META-INF/notice.txt', 'META-INF/ASL2.0']
        }
    }

    lint {
        abortOnError false
        checkReleaseBuilds false
    }
}

dependencies {
    // مكتبات Android الأساسية
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.10.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.core:core:1.12.0'
    implementation 'androidx.activity:activity:1.8.1'
    implementation 'androidx.fragment:fragment:1.6.2'

    // WebView المحسن
    implementation 'androidx.webkit:webkit:1.8.0'

    // مكتبات الشبكة
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'

    // مكتبات الصور
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.16.0'

    // مكتبات الأذونات
    implementation 'com.karumi:dexter:6.2.3'

    // مكتبات JSON
    implementation 'com.google.code.gson:gson:2.10.1'

    // مكتبات التخزين المحلي
    implementation 'androidx.preference:preference:1.2.1'

    // مكتبات الكاميرا
    implementation 'androidx.camera:camera-core:1.3.0'
    implementation 'androidx.camera:camera-camera2:1.3.0'
    implementation 'androidx.camera:camera-lifecycle:1.3.0'
    implementation 'androidx.camera:camera-view:1.3.0'

    // مكتبات الملفات
    implementation 'commons-io:commons-io:2.11.0'

    // مكتبات التشفير
    implementation 'androidx.security:security-crypto:1.1.0-alpha06'

    // مكتبات الإشعارات
    implementation 'androidx.work:work-runtime:2.8.1'

    // مكتبات التنقل
    implementation 'androidx.navigation:navigation-fragment:2.7.5'
    implementation 'androidx.navigation:navigation-ui:2.7.5'

    // مكتبات الاختبار
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    androidTestImplementation 'androidx.test:runner:1.5.2'
    androidTestImplementation 'androidx.test:rules:1.5.0'

    // مكتبات التطوير
    debugImplementation 'com.squareup.leakcanary:leakcanary-android:2.12'
}
