from django import forms
from django.utils.translation import gettext_lazy as _
from .models import Offer


class OfferForm(forms.ModelForm):
    """
    نموذج إنشاء وتعديل العروض
    """
    class Meta:
        model = Offer
        fields = ['currency', 'offer_type', 'amount', 'price', 'notes', 'expires_at']
        widgets = {
            'currency': forms.Select(attrs={
                'class': 'form-control'
            }),
            'offer_type': forms.Select(attrs={
                'class': 'form-control'
            }),
            'amount': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0.01'
            }),
            'price': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0.01'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': _('ملاحظات إضافية حول العرض (اختياري)')
            }),
            'expires_at': forms.DateTimeInput(attrs={
                'class': 'form-control',
                'type': 'datetime-local'
            }),
        }
        labels = {
            'currency': _('نوع العملة'),
            'offer_type': _('نوع العملية'),
            'amount': _('الكمية'),
            'price': _('السعر (بالدينار الجزائري)'),
            'notes': _('ملاحظات'),
            'expires_at': _('تاريخ انتهاء العرض (اختياري)'),
        }
        help_texts = {
            'amount': _('الكمية المطلوبة من العملة'),
            'price': _('السعر لكل وحدة بالدينار الجزائري'),
            'expires_at': _('تاريخ ووقت انتهاء صلاحية العرض'),
        }
    
    def clean_amount(self):
        amount = self.cleaned_data.get('amount')
        if amount and amount <= 0:
            raise forms.ValidationError(_('يجب أن تكون الكمية أكبر من صفر'))
        return amount
    
    def clean_price(self):
        price = self.cleaned_data.get('price')
        if price and price <= 0:
            raise forms.ValidationError(_('يجب أن يكون السعر أكبر من صفر'))
        return price


class OfferSearchForm(forms.Form):
    """
    نموذج البحث في العروض
    """
    currency = forms.ChoiceField(
        label=_('نوع العملة'),
        choices=[('', _('جميع العملات'))] + Offer.CURRENCY_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    offer_type = forms.ChoiceField(
        label=_('نوع العملية'),
        choices=[('', _('جميع العمليات'))] + Offer.OFFER_TYPE_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    search = forms.CharField(
        label=_('البحث'),
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('البحث في الملاحظات أو اسم المستخدم')
        })
    )
