# دليل تثبيت تطبيق تداول العملات

## المتطلبات الأساسية

### 1. تثبيت Java JDK
```bash
# تحميل Java JDK 8 أو أحدث من:
https://www.oracle.com/java/technologies/downloads/

# أو استخدام OpenJDK:
https://openjdk.org/
```

### 2. تثبيت Android Studio
```bash
# تحميل من:
https://developer.android.com/studio

# أو تثبيت Android SDK فقط:
https://developer.android.com/studio#command-tools
```

## طرق بناء APK

### الطريقة الأولى: استخدام الملف المساعد (الأسهل)

1. **فتح Command Prompt:**
   ```cmd
   cd android_app
   build_apk.bat
   ```

2. **انتظار انتهاء البناء:**
   - سيتم تنظيف المشروع تلقائياً
   - سيتم بناء APK
   - سيفتح مجلد APK تلقائياً

### الطريقة الثانية: استخدام Android Studio

1. **فتح المشروع:**
   - افتح Android Studio
   - File > Open > اختر مجلد android_app

2. **بناء APK:**
   - Build > Build Bundle(s) / APK(s) > Build APK(s)
   - انتظر انتهاء البناء

3. **العثور على APK:**
   - app/build/outputs/apk/debug/app-debug.apk

### الطريقة الثالثة: سطر الأوامر

```cmd
cd android_app
gradlew.bat clean
gradlew.bat assembleDebug
```

## تخصيص التطبيق

### 1. تغيير رابط الخادم

في ملف `app/src/main/java/com/currencyexchange/app/MainActivity.java`:

```java
// غير هذا السطر:
private static final String SERVER_URL = "https://***********:8000/";

// إلى عنوان IP الخاص بك:
private static final String SERVER_URL = "https://YOUR_IP:8000/";
```

### 2. تغيير اسم التطبيق

في ملف `app/src/main/res/values/strings.xml`:

```xml
<string name="app_name">اسم التطبيق الجديد</string>
```

### 3. تغيير الألوان

في ملف `app/src/main/res/values/colors.xml`:

```xml
<color name="primary_color">#YOUR_COLOR</color>
<color name="accent_color">#YOUR_ACCENT_COLOR</color>
```

## تثبيت APK على الهاتف

### الطريقة الأولى: نقل الملف

1. **نسخ APK:**
   - انسخ ملف `app-debug.apk` إلى الهاتف
   - يمكن استخدام USB أو البريد الإلكتروني أو التخزين السحابي

2. **تفعيل المصادر غير المعروفة:**
   - الإعدادات > الأمان > مصادر غير معروفة > تفعيل
   - أو الإعدادات > التطبيقات > إعدادات خاصة > تثبيت تطبيقات غير معروفة

3. **تثبيت التطبيق:**
   - اضغط على ملف APK
   - اضغط "تثبيت"
   - اضغط "فتح" بعد انتهاء التثبيت

### الطريقة الثانية: ADB (للمطورين)

```cmd
# تأكد من تفعيل USB Debugging
adb install app-debug.apk
```

## استكشاف الأخطاء

### خطأ: "Java not found"
```cmd
# تأكد من تثبيت Java وإضافته إلى PATH
java -version
```

### خطأ: "SDK not found"
```cmd
# تعيين متغير ANDROID_HOME
set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
set PATH=%PATH%;%ANDROID_HOME%\tools;%ANDROID_HOME%\platform-tools
```

### خطأ: "Build failed"
```cmd
# تنظيف المشروع
gradlew.bat clean
gradlew.bat assembleDebug
```

### خطأ: "App won't install"
- تأكد من تفعيل "مصادر غير معروفة"
- احذف النسخة القديمة من التطبيق
- تأكد من وجود مساحة كافية على الهاتف

## إعدادات متقدمة

### تغيير Package Name

في ملف `app/build.gradle`:

```gradle
defaultConfig {
    applicationId "com.yourcompany.yourapp"
    // ...
}
```

### إضافة أيقونة مخصصة

1. استخدم [Android Asset Studio](https://romannurik.github.io/AndroidAssetStudio/)
2. أنشئ أيقونات بأحجام مختلفة
3. استبدل الملفات في `app/src/main/res/mipmap-*/`

### توقيع التطبيق للنشر

```cmd
# إنشاء مفتاح التوقيع
keytool -genkey -v -keystore my-release-key.keystore -alias my-key-alias -keyalg RSA -keysize 2048 -validity 10000

# بناء APK موقع
gradlew.bat assembleRelease
```

## الدعم الفني

### مشاكل شائعة:

1. **التطبيق لا يتصل بالخادم:**
   - تأكد من تشغيل خادم Django
   - تحقق من عنوان IP في MainActivity.java
   - تأكد من اتصال الهاتف بنفس الشبكة

2. **الكاميرا لا تعمل:**
   - امنح أذونات الكاميرا للتطبيق
   - تأكد من استخدام HTTPS

3. **التطبيق يتوقف:**
   - تحقق من سجلات الأخطاء في Android Studio
   - تأكد من صحة رابط الخادم

### للحصول على المساعدة:
- راجع ملف README.md
- تحقق من سجلات البناء
- تأكد من تشغيل خادم Django بشكل صحيح
