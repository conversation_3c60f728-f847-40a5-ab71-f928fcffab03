# البدء السريع - تطبيق تداول العملات APK

## خطوات سريعة لبناء APK

### 1. التحضير (مرة واحدة فقط)
```cmd
# تأكد من تثبيت Java JDK 8+
java -version

# تحميل Android Studio أو Android SDK
# https://developer.android.com/studio
```

### 2. بناء APK (3 خطوات فقط)
```cmd
cd android_app
build_apk.bat
# انتظر انتهاء البناء...
```

### 3. تثبيت على الهاتف
1. انسخ `app-debug.apk` إلى الهاتف
2. فعل "مصادر غير معروفة" في الإعدادات
3. اضغط على APK لتثبيته

## تخصيص سريع

### تغيير رابط الخادم
في `MainActivity.java`:
```java
private static final String SERVER_URL = "https://YOUR_IP:8000/";
```

### تغيير اسم التطبيق
في `strings.xml`:
```xml
<string name="app_name">اسم جديد</string>
```

## مشاكل شائعة

| المشكلة | الحل |
|---------|------|
| Java not found | تثبيت Java JDK |
| Build failed | `gradlew.bat clean` ثم إعادة البناء |
| App won't install | تفعيل "مصادر غير معروفة" |
| Can't connect | تحقق من IP وتشغيل Django |

## ملفات مهمة

- `MainActivity.java` - الكود الرئيسي
- `strings.xml` - النصوص العربية
- `colors.xml` - الألوان
- `AndroidManifest.xml` - الأذونات
- `build.gradle` - إعدادات البناء

## الدعم
راجع `README.md` و `INSTALLATION_GUIDE.md` للتفاصيل الكاملة.
