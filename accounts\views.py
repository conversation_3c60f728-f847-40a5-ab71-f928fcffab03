from django.shortcuts import render, redirect
from django.contrib.auth import login, authenticate
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.views.generic import CreateView
from django.urls import reverse_lazy
from .forms import CustomUserRegistrationForm, CustomAuthenticationForm, UserProfileForm
from .models import CustomUser


def verification_guide(request):
    """
    صفحة دليل التحقق من الهوية
    """
    return render(request, 'accounts/verification_guide.html')


def camera_capture(request):
    """
    صفحة التصوير المباشر للوثائق
    """
    if request.method == 'POST':
        # معالجة البيانات المرسلة من التصوير المباشر
        return handle_camera_registration(request)

    return render(request, 'accounts/camera_capture.html')


def handle_camera_registration(request):
    """
    معالجة تسجيل المستخدم من التصوير المباشر
    """
    try:
        # إنشاء المستخدم
        user = CustomUser.objects.create_user(
            username=request.POST.get('username'),
            email=request.POST.get('email'),
            password=request.POST.get('password1'),
            document_type=request.POST.get('document_type'),
            full_name_on_document=request.POST.get('full_name_on_document')
        )

        # حفظ الصور والفيديو
        if 'document_front_image' in request.FILES:
            user.document_front_image = request.FILES['document_front_image']

        if 'document_back_image' in request.FILES:
            user.document_back_image = request.FILES['document_back_image']

        if 'selfie_with_document' in request.FILES:
            user.selfie_with_document = request.FILES['selfie_with_document']

        if 'face_verification_video' in request.FILES:
            user.face_verification_video = request.FILES['face_verification_video']

        user.save()

        messages.success(
            request,
            _('تم تسجيل حسابك بنجاح! سيتم مراجعة طلبك من قبل المشرف وستتلقى إشعاراً عند الموافقة.')
        )

        return redirect('accounts:registration_success')

    except Exception as e:
        messages.error(request, f'حدث خطأ في التسجيل: {str(e)}')
        return render(request, 'accounts/camera_capture.html')





class RegisterView(CreateView):
    """
    صفحة تسجيل مستخدم جديد
    """
    model = CustomUser
    form_class = CustomUserRegistrationForm
    template_name = 'accounts/register.html'
    success_url = reverse_lazy('accounts:registration_success')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(
            self.request,
            _('تم تسجيل حسابك بنجاح! سيتم مراجعة طلبك من قبل المشرف وستتلقى إشعاراً عند الموافقة.')
        )
        return response


def registration_success(request):
    """
    صفحة نجاح التسجيل
    """
    return render(request, 'accounts/registration_success.html')


def custom_login(request):
    """
    صفحة تسجيل الدخول المخصصة
    """
    if request.user.is_authenticated:
        return redirect('offers:offer_list')

    if request.method == 'POST':
        form = CustomAuthenticationForm(request, data=request.POST)
        if form.is_valid():
            username = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password')
            user = authenticate(username=username, password=password)

            if user is not None:
                if user.verification_status == 'approved':
                    login(request, user)
                    messages.success(request, _('مرحباً بك!'))
                    return redirect('offers:offer_list')
                elif user.verification_status == 'pending':
                    messages.warning(
                        request,
                        _('حسابك قيد المراجعة من قبل المشرف. يرجى الانتظار.')
                    )
                else:  # rejected
                    messages.error(
                        request,
                        _('تم رفض طلب التحقق من حسابك. يرجى التواصل مع الإدارة.')
                    )
            else:
                messages.error(request, _('البريد الإلكتروني أو كلمة السر غير صحيحة'))
    else:
        form = CustomAuthenticationForm()

    return render(request, 'accounts/login.html', {'form': form})


@login_required
def profile_view(request):
    """
    صفحة الملف الشخصي
    """
    if request.method == 'POST':
        form = UserProfileForm(request.POST, instance=request.user)
        if form.is_valid():
            form.save()
            messages.success(request, _('تم تحديث ملفك الشخصي بنجاح'))
            return redirect('accounts:profile')
    else:
        form = UserProfileForm(instance=request.user)

    return render(request, 'accounts/profile.html', {'form': form})


@login_required
def verification_status(request):
    """
    صفحة حالة التحقق
    """
    return render(request, 'accounts/verification_status.html')
