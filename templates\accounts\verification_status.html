{% extends 'base.html' %}

{% block title %}حالة التحقق - تداول العملات{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body p-4">
                    <h3 class="card-title text-center mb-4">
                        <i class="fas fa-user-check me-2"></i>
                        حالة التحقق من الحساب
                    </h3>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="text-center mb-4">
                                {% if user.verification_status == 'pending' %}
                                    <i class="fas fa-clock fa-4x text-warning mb-3"></i>
                                    <div class="alert verification-pending">
                                        <h5><i class="fas fa-hourglass-half me-1"></i>قيد الانتظار</h5>
                                        <p class="mb-0">حسابك قيد المراجعة من قبل المشرف</p>
                                    </div>
                                {% elif user.verification_status == 'approved' %}
                                    <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
                                    <div class="alert verification-approved">
                                        <h5><i class="fas fa-check me-1"></i>مقبول</h5>
                                        <p class="mb-0">تم قبول حسابك! يمكنك الآن إنشاء العروض</p>
                                    </div>
                                {% elif user.verification_status == 'rejected' %}
                                    <i class="fas fa-times-circle fa-4x text-danger mb-3"></i>
                                    <div class="alert verification-rejected">
                                        <h5><i class="fas fa-times me-1"></i>مرفوض</h5>
                                        <p class="mb-0">تم رفض طلب التحقق من حسابك</p>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h5>معلومات الحساب:</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>اسم المستخدم:</strong></td>
                                    <td>{{ user.username }}</td>
                                </tr>
                                <tr>
                                    <td><strong>البريد الإلكتروني:</strong></td>
                                    <td>{{ user.email }}</td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ التسجيل:</strong></td>
                                    <td>{{ user.date_joined|date:"d/m/Y" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>آخر تحديث:</strong></td>
                                    <td>{{ user.status_updated_at|date:"d/m/Y H:i" }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    {% if user.admin_notes %}
                    <div class="alert alert-info">
                        <h6><i class="fas fa-sticky-note me-1"></i>ملاحظات المشرف:</h6>
                        <p class="mb-0">{{ user.admin_notes }}</p>
                    </div>
                    {% endif %}
                    
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <h6>صورة الهوية:</h6>
                            {% if user.id_document_image %}
                                <img src="{{ user.id_document_image.url }}" class="img-fluid rounded" alt="صورة الهوية" style="max-height: 200px;">
                            {% else %}
                                <p class="text-muted">لا توجد صورة</p>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6">
                            <h6>صورة السيلفي:</h6>
                            {% if user.selfie_image %}
                                <img src="{{ user.selfie_image.url }}" class="img-fluid rounded" alt="صورة السيلفي" style="max-height: 200px;">
                            {% else %}
                                <p class="text-muted">لا توجد صورة</p>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="text-center mt-4">
                        {% if user.verification_status == 'approved' %}
                            <a href="{% url 'offers:create_offer' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                إنشاء عرض جديد
                            </a>
                        {% endif %}
                        
                        <a href="{% url 'accounts:profile' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-user-edit me-1"></i>
                            تعديل الملف الشخصي
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
