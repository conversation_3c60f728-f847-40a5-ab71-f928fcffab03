{% extends 'base.html' %}

{% block title %}تسجيل جديد - تداول العملات{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card">
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <i class="fas fa-user-plus fa-3x text-primary mb-3"></i>
                        <h3 class="card-title">إنشاء حساب جديد</h3>
                        <p class="text-muted">املأ البيانات التالية لإنشاء حسابك</p>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>مهم:</strong> يرجى قراءة
                            <a href="{% url 'accounts:verification_guide' %}" target="_blank" class="alert-link">
                                دليل التحقق من الهوية
                            </a>
                            قبل رفع الصور
                        </div>
                    </div>
                    
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.username.id_for_label }}" class="form-label">
                                    <i class="fas fa-user me-1"></i>
                                    {{ form.username.label }}
                                </label>
                                {{ form.username }}
                                {% if form.username.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.username.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.email.id_for_label }}" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>
                                    {{ form.email.label }}
                                </label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.email.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.password1.id_for_label }}" class="form-label">
                                    <i class="fas fa-lock me-1"></i>
                                    {{ form.password1.label }}
                                </label>
                                {{ form.password1 }}
                                {% if form.password1.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.password1.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.password2.id_for_label }}" class="form-label">
                                    <i class="fas fa-lock me-1"></i>
                                    {{ form.password2.label }}
                                </label>
                                {{ form.password2 }}
                                {% if form.password2.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.password2.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- قسم التحقق من الهوية -->
                        <div class="card mt-4 mb-3">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-shield-alt me-2"></i>
                                    التحقق من الهوية
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="{{ form.document_type.id_for_label }}" class="form-label">
                                        <i class="fas fa-file-alt me-1"></i>
                                        {{ form.document_type.label }}
                                    </label>
                                    {{ form.document_type }}
                                    <div class="form-text">{{ form.document_type.help_text }}</div>
                                    {% if form.document_type.errors %}
                                        <div class="text-danger small mt-1">
                                            {{ form.document_type.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.full_name_on_document.id_for_label }}" class="form-label">
                                        <i class="fas fa-signature me-1"></i>
                                        {{ form.full_name_on_document.label }}
                                    </label>
                                    {{ form.full_name_on_document }}
                                    <div class="form-text">{{ form.full_name_on_document.help_text }}</div>
                                    {% if form.full_name_on_document.errors %}
                                        <div class="text-danger small mt-1">
                                            {{ form.full_name_on_document.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="{{ form.document_front_image.id_for_label }}" class="form-label">
                                            <i class="fas fa-id-card me-1"></i>
                                            {{ form.document_front_image.label }}
                                        </label>
                                        {{ form.document_front_image }}
                                        <div class="form-text">{{ form.document_front_image.help_text }}</div>
                                        {% if form.document_front_image.errors %}
                                            <div class="text-danger small mt-1">
                                                {{ form.document_front_image.errors.0 }}
                                            </div>
                                        {% endif %}
                                    </div>

                                    <div class="col-md-6 mb-3" id="back-image-field">
                                        <label for="{{ form.document_back_image.id_for_label }}" class="form-label">
                                            <i class="fas fa-id-card me-1"></i>
                                            {{ form.document_back_image.label }}
                                        </label>
                                        {{ form.document_back_image }}
                                        <div class="form-text">{{ form.document_back_image.help_text }}</div>
                                        {% if form.document_back_image.errors %}
                                            <div class="text-danger small mt-1">
                                                {{ form.document_back_image.errors.0 }}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.selfie_with_document.id_for_label }}" class="form-label">
                                        <i class="fas fa-camera me-1"></i>
                                        {{ form.selfie_with_document.label }}
                                    </label>
                                    {{ form.selfie_with_document }}
                                    <div class="form-text">{{ form.selfie_with_document.help_text }}</div>
                                    {% if form.selfie_with_document.errors %}
                                        <div class="text-danger small mt-1">
                                            {{ form.selfie_with_document.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors.0 }}
                            </div>
                        {% endif %}
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-1"></i>
                            <strong>ملاحظة مهمة:</strong> سيتم مراجعة طلبك من قبل المشرف قبل تفعيل حسابك.
                        </div>
                        
                        <div class="d-grid mb-3">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-user-plus me-1"></i>
                                إنشاء الحساب
                            </button>
                        </div>
                        
                        <div class="text-center">
                            <p class="mb-0">لديك حساب بالفعل؟</p>
                            <a href="{% url 'accounts:login' %}" class="btn btn-outline-primary">
                                <i class="fas fa-sign-in-alt me-1"></i>
                                تسجيل الدخول
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleBackImageField() {
    const documentType = document.getElementById('id_document_type').value;
    const backImageField = document.getElementById('back-image-field');
    const backImageInput = document.getElementById('id_document_back_image');

    if (documentType === 'passport') {
        // جواز السفر لا يحتاج صورة خلفية
        backImageField.style.display = 'none';
        backImageInput.required = false;
    } else {
        // بطاقة الهوية ورخصة السياقة تحتاج صورة خلفية
        backImageField.style.display = 'block';
        backImageInput.required = true;
    }
}

// تشغيل الدالة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    toggleBackImageField();
});
</script>
{% endblock %}
