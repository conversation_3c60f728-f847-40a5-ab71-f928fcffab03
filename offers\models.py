from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator
from decimal import Decimal

User = get_user_model()


class Offer(models.Model):
    """
    نموذج العروض لبيع وشراء العملات
    """
    CURRENCY_CHOICES = [
        ('USD', _('دولار أمريكي')),
        ('EUR', _('يورو')),
        ('DZD', _('دينار جزائري')),
    ]

    OFFER_TYPE_CHOICES = [
        ('buy', _('شراء')),
        ('sell', _('بيع')),
    ]

    STATUS_CHOICES = [
        ('active', _('نشط')),
        ('inactive', _('غير نشط')),
        ('completed', _('مكتمل')),
        ('cancelled', _('ملغي')),
    ]

    # صاحب العرض
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        verbose_name=_('صاحب العرض'),
        related_name='offers'
    )

    # نوع العملة
    currency = models.CharField(
        _('نوع العملة'),
        max_length=3,
        choices=CURRENCY_CHOICES
    )

    # نوع العملية
    offer_type = models.CharField(
        _('نوع العملية'),
        max_length=4,
        choices=OFFER_TYPE_CHOICES
    )

    # السعر
    price = models.DecimalField(
        _('السعر'),
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text=_('السعر بالدينار الجزائري')
    )

    # الكمية
    amount = models.DecimalField(
        _('الكمية'),
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text=_('الكمية المطلوبة')
    )

    # الكمية المتبقية
    remaining_amount = models.DecimalField(
        _('الكمية المتبقية'),
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.00'))],
        help_text=_('الكمية المتبقية من العرض')
    )

    # ملاحظات
    notes = models.TextField(
        _('ملاحظات'),
        blank=True,
        null=True,
        help_text=_('ملاحظات إضافية حول العرض')
    )

    # حالة العرض
    status = models.CharField(
        _('حالة العرض'),
        max_length=10,
        choices=STATUS_CHOICES,
        default='active'
    )

    # تاريخ النشر
    created_at = models.DateTimeField(_('تاريخ النشر'), auto_now_add=True)

    # تاريخ آخر تحديث
    updated_at = models.DateTimeField(_('تاريخ آخر تحديث'), auto_now=True)

    # تاريخ انتهاء العرض (اختياري)
    expires_at = models.DateTimeField(
        _('تاريخ انتهاء العرض'),
        blank=True,
        null=True,
        help_text=_('تاريخ انتهاء صلاحية العرض (اختياري)')
    )

    class Meta:
        verbose_name = _('عرض')
        verbose_name_plural = _('العروض')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.get_offer_type_display()} {self.amount} {self.get_currency_display()} - {self.price} دج"

    def save(self, *args, **kwargs):
        # تعيين الكمية المتبقية عند الإنشاء
        if not self.pk:
            self.remaining_amount = self.amount
        super().save(*args, **kwargs)

    @property
    def is_available(self):
        """التحقق من توفر العرض"""
        return (
            self.status == 'active' and
            self.remaining_amount > 0 and
            self.user.is_verified
        )

    @property
    def completion_percentage(self):
        """نسبة اكتمال العرض"""
        if self.amount == 0:
            return 0
        return ((self.amount - self.remaining_amount) / self.amount) * 100
