{% extends 'base.html' %}

{% block title %}الملف الشخصي - تداول العملات{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body p-4">
                    <h3 class="card-title mb-4">
                        <i class="fas fa-user-edit me-2"></i>
                        تعديل الملف الشخصي
                    </h3>
                    
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.username.id_for_label }}" class="form-label">
                                    {{ form.username.label }}
                                </label>
                                {{ form.username }}
                                {% if form.username.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.username.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.email.id_for_label }}" class="form-label">
                                    {{ form.email.label }}
                                </label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.email.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.first_name.id_for_label }}" class="form-label">
                                    {{ form.first_name.label }}
                                </label>
                                {{ form.first_name }}
                                {% if form.first_name.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.first_name.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.last_name.id_for_label }}" class="form-label">
                                    {{ form.last_name.label }}
                                </label>
                                {{ form.last_name }}
                                {% if form.last_name.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.last_name.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="card-title">معلومات الحساب</h5>
                    
                    <div class="mb-3">
                        {% if user.verification_status == 'pending' %}
                            <span class="badge bg-warning fs-6">قيد الانتظار</span>
                        {% elif user.verification_status == 'approved' %}
                            <span class="badge bg-success fs-6">مقبول</span>
                        {% elif user.verification_status == 'rejected' %}
                            <span class="badge bg-danger fs-6">مرفوض</span>
                        {% endif %}
                    </div>
                    
                    <p><strong>تاريخ التسجيل:</strong><br>{{ user.date_joined|date:"d/m/Y" }}</p>
                    
                    <div class="d-grid gap-2">
                        <a href="{% url 'accounts:verification_status' %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-check-circle me-1"></i>
                            حالة التحقق
                        </a>
                        
                        {% if user.is_verified %}
                        <a href="{% url 'offers:my_offers' %}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-list me-1"></i>
                            عروضي
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
