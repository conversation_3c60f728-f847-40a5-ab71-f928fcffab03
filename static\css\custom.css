/* تخصيص CSS للتطبيق */

/* الخط العربي */
body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    text-align: right;
}

/* تحسين الأزرار */
.btn {
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* تحسين البطاقات */
.card {
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: none;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* تحسين النماذج */
.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

/* تحسين شريط التنقل */
.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* تحسين الجداول */
.table {
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
}

/* تحسين التنبيهات */
.alert {
    border-radius: 8px;
    border: none;
}

/* تحسين الشارات */
.badge {
    font-size: 0.8rem;
    padding: 0.5em 0.75em;
    border-radius: 6px;
}

/* تحسين الصور */
.img-thumbnail {
    border-radius: 8px;
    border: 2px solid #dee2e6;
}

/* تحسين الأيقونات */
.fas, .far {
    margin-left: 5px;
}

/* تحسين الروابط */
a {
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    text-decoration: none;
}

/* تحسين الفواصل */
hr {
    border-top: 2px solid #dee2e6;
    border-radius: 2px;
}

/* تحسين الحاويات */
.container {
    max-width: 1200px;
}

/* تحسين الأزرار الصغيرة */
.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 6px;
}

/* تحسين الأزرار الكبيرة */
.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.125rem;
    border-radius: 10px;
}

/* تحسين الحقول المطلوبة */
.required {
    color: #dc3545;
}

/* تحسين رسائل الخطأ */
.text-danger {
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* تحسين النصوص المساعدة */
.form-text {
    font-size: 0.875rem;
    color: #6c757d;
}

/* تحسين الحالات */
.status-pending {
    color: #ffc107;
}

.status-approved {
    color: #28a745;
}

.status-rejected {
    color: #dc3545;
}

/* تحسين الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
}

.stats-card .card-body {
    padding: 1.5rem;
}

.stats-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

/* تحسين الفلاتر */
.filter-section {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

/* تحسين الباجينيشن */
.pagination {
    border-radius: 8px;
}

.page-link {
    border-radius: 6px;
    margin: 0 2px;
    border: none;
    color: #007bff;
}

.page-link:hover {
    background-color: #e9ecef;
    color: #0056b3;
}

.page-item.active .page-link {
    background-color: #007bff;
    border-color: #007bff;
}

/* تحسين الحاسبة */
.calculator-result {
    background-color: #e7f3ff;
    border: 2px solid #007bff;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
}

/* تحسين الرسائل */
.message-bubble {
    border-radius: 18px;
    padding: 0.75rem 1rem;
    margin-bottom: 0.5rem;
    max-width: 70%;
}

.message-sent {
    background-color: #007bff;
    color: white;
    margin-right: auto;
}

.message-received {
    background-color: #f8f9fa;
    color: #333;
    margin-left: auto;
}

/* تحسين الحالة المتصلة */
.online-indicator {
    width: 10px;
    height: 10px;
    background-color: #28a745;
    border-radius: 50%;
    display: inline-block;
    margin-right: 5px;
}

/* تحسين الحالة غير المتصلة */
.offline-indicator {
    width: 10px;
    height: 10px;
    background-color: #6c757d;
    border-radius: 50%;
    display: inline-block;
    margin-right: 5px;
}

/* تحسين الاستجابة للموبايل */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .stats-number {
        font-size: 1.5rem;
    }
}

/* تحسين الطباعة */
@media print {
    .no-print {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
}

/* تحسينات خاصة بالكاميرا */
.camera-container {
    position: relative;
    background-color: #000;
    border-radius: 12px;
    overflow: hidden;
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.camera-container video {
    width: 100%;
    height: auto;
    max-height: 400px;
    object-fit: cover;
    border-radius: 12px;
}

.camera-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 5;
}

.document-frame {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80%;
    height: 60%;
    border: 3px solid #28a745;
    border-radius: 8px;
    box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.3);
}

.selfie-frame {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
    border: 3px solid #007bff;
    border-radius: 50%;
    box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.3);
}

.face-frame {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 180px;
    height: 180px;
    border: 3px solid #dc3545;
    border-radius: 50%;
    box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.3);
}

.camera-instructions {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px 20px;
    border-radius: 20px;
    font-size: 14px;
    text-align: center;
    white-space: nowrap;
}

.preview-container {
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border-radius: 12px;
    border: 2px dashed #dee2e6;
}

.preview-placeholder {
    text-align: center;
    color: #6c757d;
}

.document-type-card {
    cursor: pointer;
    transition: all 0.3s ease;
    opacity: 0.7;
    pointer-events: none;
}

.document-type-card.camera-ready {
    opacity: 1;
    pointer-events: auto;
}

.document-type-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: #007bff;
}

.document-type-card.selected {
    border-color: #28a745;
    background-color: #f8fff9;
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.2);
}

.step-content {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.progress-bar {
    transition: width 0.5s ease-in-out;
}

/* تحسينات للموبايل - الكاميرا */
@media (max-width: 768px) {
    .camera-container {
        min-height: 250px;
    }
    
    .camera-container video {
        max-height: 300px;
    }
    
    .document-frame {
        width: 90%;
        height: 70%;
    }
    
    .selfie-frame,
    .face-frame {
        width: 150px;
        height: 150px;
    }
    
    .camera-instructions {
        font-size: 12px;
        padding: 8px 16px;
        bottom: 10px;
    }
    
    .preview-container {
        min-height: 200px;
    }
}

/* تحسين رسائل الخطأ والتحميل */
.camera-loading-overlay,
.camera-error-overlay {
    border-radius: 12px;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* تحسين أزرار الكاميرا */
.btn-camera {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-camera:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.3);
}

.btn-camera:active {
    transform: translateY(0);
}

/* تحسين مؤشر التسجيل */
.recording-indicator {
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}
