# Generated by Django 5.2 on 2025-07-27 14:34

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('offers', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Conversation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ البداية')),
                ('last_message_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ آخر رسالة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشطة')),
                ('offer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='conversations', to='offers.offer', verbose_name='العرض')),
                ('participant1', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='conversations_as_participant1', to=settings.AUTH_USER_MODEL, verbose_name='المشارك الأول')),
                ('participant2', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='conversations_as_participant2', to=settings.AUTH_USER_MODEL, verbose_name='المشارك الثاني')),
            ],
            options={
                'verbose_name': 'محادثة',
                'verbose_name_plural': 'المحادثات',
                'ordering': ['-last_message_at'],
                'unique_together': {('participant1', 'participant2', 'offer')},
            },
        ),
        migrations.CreateModel(
            name='Message',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content', models.TextField(verbose_name='محتوى الرسالة')),
                ('sent_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإرسال')),
                ('is_read', models.BooleanField(default=False, verbose_name='مقروءة')),
                ('read_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ القراءة')),
                ('conversation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='messaging.conversation', verbose_name='المحادثة')),
                ('sender', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sent_messages', to=settings.AUTH_USER_MODEL, verbose_name='المرسل')),
            ],
            options={
                'verbose_name': 'رسالة',
                'verbose_name_plural': 'الرسائل',
                'ordering': ['sent_at'],
            },
        ),
    ]
