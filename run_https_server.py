#!/usr/bin/env python3
"""
تشغيل خادم Django مع HTTPS
"""

import os
import sys
import ssl
import socket
from pathlib import Path
import django
from django.core.management import execute_from_command_line
from django.core.wsgi import get_wsgi_application
from wsgiref.simple_server import make_server, WSGIServer
from wsgiref.simple_server import WSGIRequestHandler

class HTTPSServer(WSGIServer):
    """خادم HTTPS مخصص"""
    
    def __init__(self, server_address, RequestHandlerClass, bind_and_activate=True):
        WSGIServer.__init__(self, server_address, RequestHandlerClass, bind_and_activate)
        
        # إعداد SSL
        context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
        context.load_cert_chain('server.crt', 'server.key')
        
        self.socket = context.wrap_socket(self.socket, server_side=True)

def get_local_ip():
    """الحصول على IP المحلي"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "***********"

def run_https_server():
    """تشغيل خادم HTTPS"""
    
    # التحقق من وجود الشهادة
    if not (Path('server.crt').exists() and Path('server.key').exists()):
        print("❌ الشهادة غير موجودة. جاري إنشاؤها...")
        os.system('python create_ssl_cert.py')
        
        if not (Path('server.crt').exists() and Path('server.key').exists()):
            print("❌ فشل في إنشاء الشهادة")
            return
    
    # إعداد Django
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'currency_exchange.settings')
    django.setup()
    
    # الحصول على تطبيق WSGI
    application = get_wsgi_application()
    
    # إعداد الخادم
    local_ip = get_local_ip()
    port = 8000
    
    try:
        # إنشاء خادم HTTPS
        httpd = make_server(local_ip, port, application, server_class=HTTPSServer)
        
        print(f"""
🔐 تم تشغيل خادم HTTPS بنجاح!

🌐 الروابط:
- للكمبيوتر: https://localhost:{port}/
- للهاتف: https://{local_ip}:{port}/

⚠️ ملاحظات مهمة:
1. ستظهر تحذيرات أمنية في المتصفح - هذا طبيعي
2. اضغط "متقدم" ثم "المتابعة إلى الموقع"
3. في الهاتف، اضغط "متقدم" ثم "المتابعة"

🛑 لإيقاف الخادم: اضغط Ctrl+C
""")
        
        httpd.serve_forever()
        
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
        print("\n🔄 جاري المحاولة مع HTTP العادي...")
        run_http_fallback()

def run_http_fallback():
    """تشغيل HTTP عادي كبديل"""
    local_ip = get_local_ip()
    os.system(f'python manage.py runserver {local_ip}:8000')

if __name__ == "__main__":
    run_https_server()