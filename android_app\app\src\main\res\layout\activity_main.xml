<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/primary_color"
    tools:context=".MainActivity">

    <!-- شريط التحميل -->
    <ProgressBar
        android:id="@+id/progressBar"
        style="?android:attr/progressBarStyleLarge"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:indeterminateTint="@color/accent_color"
        android:visibility="visible" />

    <!-- النص التوضيحي -->
    <TextView
        android:id="@+id/loadingText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/progressBar"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="16dp"
        android:text="@string/loading_text"
        android:textColor="@color/text_color"
        android:textSize="16sp"
        android:fontFamily="@font/arabic_font" />

    <!-- WebView الرئيسي -->
    <WebView
        android:id="@+id/webview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true"
        android:background="@color/background_color" />

    <!-- شريط الحالة العلوي -->
    <LinearLayout
        android:id="@+id/statusBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:background="@color/primary_dark"
        android:orientation="horizontal"
        android:padding="8dp"
        android:visibility="gone">

        <!-- أيقونة الاتصال -->
        <ImageView
            android:id="@+id/connectionIcon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="center_vertical"
            android:src="@drawable/ic_wifi"
            android:tint="@color/white" />

        <!-- نص الحالة -->
        <TextView
            android:id="@+id/statusText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="8dp"
            android:layout_weight="1"
            android:text="@string/connected"
            android:textColor="@color/white"
            android:textSize="14sp" />

        <!-- زر الإعدادات -->
        <ImageButton
            android:id="@+id/settingsButton"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_gravity="center_vertical"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_settings"
            android:tint="@color/white" />

    </LinearLayout>

    <!-- شريط التنقل السفلي -->
    <LinearLayout
        android:id="@+id/navigationBar"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:layout_alignParentBottom="true"
        android:background="@color/primary_dark"
        android:orientation="horizontal"
        android:visibility="gone">

        <!-- زر الرجوع -->
        <ImageButton
            android:id="@+id/backButton"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="?android:attr/selectableItemBackground"
            android:src="@drawable/ic_arrow_back"
            android:tint="@color/white" />

        <!-- زر التقديم -->
        <ImageButton
            android:id="@+id/forwardButton"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="?android:attr/selectableItemBackground"
            android:src="@drawable/ic_arrow_forward"
            android:tint="@color/white" />

        <!-- زر التحديث -->
        <ImageButton
            android:id="@+id/refreshButton"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="?android:attr/selectableItemBackground"
            android:src="@drawable/ic_refresh"
            android:tint="@color/white" />

        <!-- زر الرئيسية -->
        <ImageButton
            android:id="@+id/homeButton"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="?android:attr/selectableItemBackground"
            android:src="@drawable/ic_home"
            android:tint="@color/white" />

    </LinearLayout>

</RelativeLayout>
