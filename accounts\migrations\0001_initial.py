# Generated by Django 5.2 on 2025-07-27 14:34

import django.contrib.auth.models
import django.contrib.auth.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.Char<PERSON>ield(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('email', models.EmailField(max_length=254, unique=True, verbose_name='البريد الإلكتروني')),
                ('id_document_image', models.ImageField(help_text='يرجى رفع صورة واضحة لبطاقة الهوية أو جواز السفر أو رخصة السياقة', upload_to='id_documents/', verbose_name='صورة بطاقة الهوية/جواز السفر/رخصة السياقة')),
                ('selfie_image', models.ImageField(help_text='يرجى رفع صورة سيلفي واضحة للوجه', upload_to='selfies/', verbose_name='صورة سيلفي للوجه')),
                ('verification_status', models.CharField(choices=[('pending', 'قيد الانتظار'), ('approved', 'مقبول'), ('rejected', 'مرفوض')], default='pending', max_length=20, verbose_name='حالة التحقق')),
                ('date_joined', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التسجيل')),
                ('status_updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ تحديث الحالة')),
                ('admin_notes', models.TextField(blank=True, help_text='ملاحظات خاصة بالمشرف حول حالة التحقق', null=True, verbose_name='ملاحظات المشرف')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'مستخدم',
                'verbose_name_plural': 'المستخدمون',
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
    ]
