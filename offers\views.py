from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.core.paginator import Paginator
from django.db.models import Q
from .models import Offer
from .forms import OfferForm


def offer_list(request):
    """
    صفحة قائمة العروض
    """
    offers = Offer.objects.filter(status='active', remaining_amount__gt=0)

    # فلترة حسب نوع العملة
    currency = request.GET.get('currency')
    if currency:
        offers = offers.filter(currency=currency)

    # فلترة حسب نوع العملية
    offer_type = request.GET.get('offer_type')
    if offer_type:
        offers = offers.filter(offer_type=offer_type)

    # البحث
    search = request.GET.get('search')
    if search:
        offers = offers.filter(
            Q(notes__icontains=search) |
            Q(user__username__icontains=search)
        )

    # ترتيب العروض
    offers = offers.order_by('-created_at')

    # تقسيم الصفحات
    paginator = Paginator(offers, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'currency_choices': Offer.CURRENCY_CHOICES,
        'offer_type_choices': Offer.OFFER_TYPE_CHOICES,
        'current_currency': currency,
        'current_offer_type': offer_type,
        'current_search': search,
    }

    return render(request, 'offers/offer_list.html', context)


def offer_detail(request, pk):
    """
    صفحة تفاصيل العرض
    """
    offer = get_object_or_404(Offer, pk=pk)

    context = {
        'offer': offer,
        'can_contact': request.user.is_authenticated and request.user != offer.user and request.user.is_verified
    }

    return render(request, 'offers/offer_detail.html', context)


@login_required
def create_offer(request):
    """
    صفحة إنشاء عرض جديد
    """
    if not request.user.is_verified:
        messages.error(request, _('يجب أن يكون حسابك مقبولاً لإنشاء عروض'))
        return redirect('accounts:verification_status')

    if request.method == 'POST':
        form = OfferForm(request.POST)
        if form.is_valid():
            offer = form.save(commit=False)
            offer.user = request.user
            offer.save()
            messages.success(request, _('تم إنشاء العرض بنجاح'))
            return redirect('offers:offer_detail', pk=offer.pk)
    else:
        form = OfferForm()

    return render(request, 'offers/create_offer.html', {'form': form})


@login_required
def my_offers(request):
    """
    صفحة عروض المستخدم
    """
    offers = Offer.objects.filter(user=request.user).order_by('-created_at')

    # إحصائيات العروض
    total_offers = offers.count()
    active_offers = offers.filter(status='active').count()
    completed_offers = offers.filter(status='completed').count()
    inactive_offers = offers.filter(status='inactive').count()

    # تقسيم الصفحات
    paginator = Paginator(offers, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'offers': page_obj,
        'total_offers': total_offers,
        'active_offers': active_offers,
        'completed_offers': completed_offers,
        'inactive_offers': inactive_offers,
    }

    return render(request, 'offers/my_offers.html', context)


@login_required
def edit_offer(request, pk):
    """
    صفحة تعديل العرض
    """
    offer = get_object_or_404(Offer, pk=pk, user=request.user)

    if request.method == 'POST':
        form = OfferForm(request.POST, instance=offer)
        if form.is_valid():
            form.save()
            messages.success(request, _('تم تحديث العرض بنجاح'))
            return redirect('offers:offer_detail', pk=offer.pk)
    else:
        form = OfferForm(instance=offer)

    return render(request, 'offers/edit_offer.html', {'form': form, 'offer': offer})


@login_required
def delete_offer(request, pk):
    """
    حذف العرض
    """
    offer = get_object_or_404(Offer, pk=pk, user=request.user)

    if request.method == 'POST':
        offer.delete()
        messages.success(request, _('تم حذف العرض بنجاح'))
        return redirect('offers:my_offers')

    return render(request, 'offers/delete_offer.html', {'offer': offer})
